#!/usr/bin/env python3
"""
Simple Recoater Connection Test
==============================

This is the SIMPLEST possible program to test connection to the recoater system.
It makes a single API call to check if the recoater is responding.

For beginners: This is like saying "Hello" to the recoater to see if it's listening.

What this program does:
1. Tries to connect to the recoater's API
2. Asks for the system status
3. Prints whether the connection worked or not

How to use:
1. Make sure your recoater system is running
2. Make sure your network connection is set up (see MANUAL.md)
3. Run this program: python simple_connection_test.py
"""

import requests
import json
from config import API_BASE_URL, API_TIMEOUT

def test_connection():
    """
    Test connection to recoater by getting system state.
    This is the simplest API call we can make.
    """
    print("=" * 50)
    print("RECOATER CONNECTION TEST")
    print("=" * 50)
    print(f"Trying to connect to: {API_BASE_URL}")
    print()
    
    try:
        # Make the simplest possible API call - get system state
        print("Step 1: Sending request to /state endpoint...")
        response = requests.get(
            f"{API_BASE_URL}/state",
            timeout=API_TIMEOUT
        )
        
        print(f"Step 2: Got response with status code: {response.status_code}")
        
        # Check if the request was successful
        if response.status_code == 200:
            # Parse the JSON response
            state_data = response.json()
            
            print("✅ SUCCESS! Connection to recoater established!")
            print()
            print("Recoater System Information:")
            print("-" * 30)
            print(f"System State: {state_data.get('state', 'Unknown')}")
            print()
            print("Raw API Response:")
            print(json.dumps(state_data, indent=2))
            
            return True
            
        else:
            print(f"❌ FAILED! Got error status code: {response.status_code}")
            print(f"Error message: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION FAILED!")
        print("Could not connect to the recoater system.")
        print()
        print("Possible causes:")
        print("1. Recoater system is not running")
        print("2. Wrong IP address in config.py")
        print("3. Network connection not set up correctly")
        print("4. USB3-to-RJ45 adapter not working")
        print()
        print("Check the MANUAL.md file for setup instructions.")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ TIMEOUT!")
        print("The recoater system didn't respond in time.")
        print("It might be busy or having problems.")
        return False
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        print("Something unexpected happened.")
        return False

def main():
    """Main function - runs the connection test."""
    success = test_connection()
    
    print()
    print("=" * 50)
    if success:
        print("🎉 CONGRATULATIONS!")
        print("Your connection to the recoater is working!")
        print("You can now proceed to more advanced programs.")
    else:
        print("😞 CONNECTION FAILED")
        print("Please check the setup and try again.")
        print("See MANUAL.md for troubleshooting help.")
    print("=" * 50)

if __name__ == "__main__":
    main()
