<template>
  <div class="control-card">
    <!-- Card Header -->
    <div class="card-header">
      <h3 class="card-title">Leveler Control</h3>
      <div class="status-indicator">
        <div :class="['status-dot', connected ? 'status-connected' : 'status-disconnected']"></div>
        <span class="status-text">{{ connected ? 'Ready' : 'Disconnected' }}</span>
      </div>
    </div>

    <div class="card-content">
      <!-- Pressure Control -->
      <div class="control-group">
        <h4 class="group-title">Pressure Control</h4>
        <div class="info-grid three-cols">
          <div class="info-box">
            <label class="info-label">Current</label>
            <div class="info-value">{{ pressureData?.value?.toFixed(2) || '--' }} Pa</div>
          </div>
          <div class="info-box">
            <label class="info-label">Target</label>
            <div class="info-value">{{ pressureData?.target?.toFixed(2) || '--' }} Pa</div>
          </div>
          <div class="info-box">
            <label class="info-label">Maximum</label>
            <div class="info-value">{{ pressureData?.maximum?.toFixed(2) || '--' }} Pa</div>
          </div>
        </div>
        <div class="pressure-set mt-4">
          <input
            v-model.number="targetPressure"
            type="number"
            step="0.1"
            min="0"
            :max="pressureData?.maximum || 10"
            class="form-input-sm"
            :disabled="!connected || isSettingPressure"
            placeholder="Target Pressure (Pa)"
          />
          <button @click="setPressure" :disabled="!connected || isSettingPressure || !isValidPressure" class="btn-set bg-blue-500 hover:bg-blue-600">
            <span v-if="isSettingPressure">Setting...</span>
            <span v-else>Set</span>
          </button>
        </div>
      </div>

      <!-- Sensor Status -->
      <div class="control-group">
        <h4 class="group-title">Magnetic Sensor</h4>
        <div class="info-box">
          <label class="info-label">Sensor State</label>
          <div class="info-value flex items-center gap-2">
            <div :class="['status-dot', sensorData?.state ? 'status-running' : 'status-stopped']"></div>
            <span>{{ sensorData?.state ? 'Active (Field Detected)' : 'Inactive' }}</span>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="errorMessage" class="error-message mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import apiService from '../services/api';

export default {
  name: 'LevelerControl',
  props: {
    pressureData: { type: Object, default: () => ({}) },
    sensorData: { type: Object, default: () => ({}) },
    connected: { type: Boolean, default: false }
  },
  emits: ['error', 'success', 'pressure-set'],
  setup(props, { emit }) {
    const targetPressure = ref('');
    const isSettingPressure = ref(false);
    const errorMessage = ref('');
    const errorTimeout = ref(null);

    const isValidPressure = computed(() => {
      const p = parseFloat(targetPressure.value);
      return !isNaN(p) && p >= 0 && p <= (props.pressureData?.maximum || 1000);
    });

    const showError = (error) => {
      const message = error?.response?.data?.detail || error?.message || 'An error occurred';
      errorMessage.value = message;
      if (errorTimeout.value) {
        clearTimeout(errorTimeout.value);
      }
      errorTimeout.value = setTimeout(() => {
        errorMessage.value = '';
      }, 5000);
    };

    const setPressure = async () => {
      if (!isValidPressure.value || isSettingPressure.value) return;
      isSettingPressure.value = true;
      try {
        const pressure = parseFloat(targetPressure.value);
        await apiService.setLevelerPressure(pressure);
        emit('pressure-set', { target: pressure });
        emit('success', `Leveler pressure set to ${pressure} Pa`);
        targetPressure.value = '';
      } catch (error) {
        showError(error);
        emit('error', error);
      } finally {
        isSettingPressure.value = false;
      }
    };

    return {
      targetPressure,
      isSettingPressure,
      isValidPressure,
      errorMessage,
      setPressure,
    };
  }
};
</script>

<style scoped>
/* Card styles are now consistent with DrumControl */
.control-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
}
.control-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}
.card-header { display: flex; justify-content: space-between; align-items: center; padding: 1rem 1.5rem; border-bottom: 1px solid #e5e7eb; }
.card-title { font-size: 1.1rem; font-weight: 600; color: #1f2937; }
.status-indicator { display: flex; align-items: center; gap: 0.5rem; }
.status-dot { width: 10px; height: 10px; border-radius: 50%; }
.status-connected, .status-running { background-color: #10b981; }
.status-disconnected, .status-stopped { background-color: #6b7280; }
.status-text { font-size: 0.875rem; color: #4b5563; font-weight: 500; }
.card-content { padding: 0rem 1rem 1rem 1rem; }
.info-grid { display: grid; gap: 1rem; margin-bottom: 1rem; }
.info-grid.three-cols { grid-template-columns: repeat(3, 1fr); }
.info-box { background-color: #f9fafb; padding: 0.75rem; border-radius: 6px; border: 1px solid #f3f4f6; }
.info-label { display: block; font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem; text-transform: uppercase; letter-spacing: 0.05em; }
.info-value { font-size: 1rem; font-weight: 500; color: #1f2937; }
.control-group { margin-bottom: 1.5rem; }
.control-group:last-child { margin-bottom: 0; }
.group-title { font-size: 1rem; font-weight: 600; color: #374151; margin-bottom: 1rem; }
.pressure-set { display: flex; gap: 0.5rem; align-items: center; }
.form-input-sm { flex-grow: 1; padding: 0.6rem; border: 1px solid #d1d5db; border-radius: 6px; font-size: 0.875rem; }
.btn-set { 
  background-color: var(--color-primary) !important;
  color: white; 
  border: none; 
  border-radius: 6px; 
  padding: 0.6rem 1rem; 
  cursor: pointer; 
  font-weight: 500; 
  transition: background-color 0.2s; 
}

.btn-set:hover:not(:disabled) {
  background-color: var(--color-primary-hover) !important;
}

.btn-set:disabled { 
  opacity: 0.5; 
  cursor: not-allowed; 
}
</style>