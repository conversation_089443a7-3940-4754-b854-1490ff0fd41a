# Multi-Material Print Job Implementation Guide

## Overview

This documentation provides comprehensive guidance for implementing multi-material print job functionality in the Recoater HMI system. The implementation enables automated layer-by-layer printing coordination across 3 drums between the FastAPI backend, TwinCAT PLC, and Aerosint recoater system.

**Multi-Material System Highlights:**
- **3-Drum Configuration**: Coordinates 3 separate material drums simultaneously
- **Synchronized Operation**: Ensures all drums are ready before each layer execution
- **Material Coordination**: Manages different materials across drums for complex prints
- **Flexible Layer Management**: Handles 3 CLI files with different layer counts, uses maximum as total_layers

## Documentation Structure

### Core Implementation Guides
- **[Project Context](./01-project-context.md)** - Background, multi-material requirements, and current system analysis
- **[Architecture Design](./02-architecture-design.md)** - Multi-drum system architecture and component interactions
- **[Implementation Stages](./03-implementation-stages.md)** - Step-by-step implementation guide for 3-drum coordination
- **[Code Changes](./04-code-changes.md)** - Detailed code modifications for multi-material operation
- **[Testing Guide](./05-testing-guide.md)** - Testing procedures and validation for 3-drum system
- **[Deployment Guide](./06-deployment-guide.md)** - Production deployment instructions for multi-material setup

### AI Agent Resources
- **[AI Agent Guidelines](./AI-AGENT-GUIDELINES.md)** - Specific guidance for AI agents implementing this system
- **[Implementation Status](./IMPLEMENTATION-STATUS.md)** - Current status and what needs to be implemented
- **[Development Environment Setup](./DEV-ENVIRONMENT-SETUP.md)** - Complete development environment configuration

## Quick Start

### For Human Developers
1. **Read Project Context** - Understand the multi-material problem and 3-drum requirements
2. **Review Architecture** - Understand the multi-drum system design
3. **Follow Implementation Stages** - Implement in order (Stage 1 → Stage 4)
4. **Test Each Stage** - Validate functionality before proceeding (include 3-drum testing)
5. **Deploy to Production** - Follow deployment guidelines for multi-material system

### For AI Agents
1. **Start with [AI Agent Guidelines](./AI-AGENT-GUIDELINES.md)** - Essential patterns and principles
2. **Check [Implementation Status](./IMPLEMENTATION-STATUS.md)** - See what's done vs. what needs implementation
3. **Set up [Development Environment](./DEV-ENVIRONMENT-SETUP.md)** - Complete environment configuration
4. **Follow Stage-by-Stage Implementation** - Must complete Stage 1 → 2 → 3 → 4 in order
5. **Use Existing Patterns** - Leverage established code patterns from current codebase

## Implementation Overview

- **Stage 1**: OPC UA Infrastructure (PLC communication setup)
- **Stage 2**: Multi-Material Job Management (3-drum coordination logic)
- **Stage 3**: Coordination Logic (3-drum synchronization and layer progression)
- **Stage 4**: Frontend Integration (multi-material user interface)

**Note**: Implementation timelines have been removed per request. Focus on completing each stage thoroughly before proceeding to the next.

## Prerequisites

- Existing Recoater HMI system (FastAPI + Vue.js)
- TwinCAT PLC with OPC UA server capability for 3-drum coordination
- Aerosint recoater with 3-drum configuration and REST API
- Python 3.8+ with asyncio support
- Vue.js 3+ frontend framework

## Key Multi-Material Features

- **3-File Upload**: Upload and validate 3 CLI files (one per drum)
- **Automated Multi-Drum Processing**: No manual intervention between layers across all drums
- **Real-time 3-Drum Coordination**: OPC UA-based communication with PLC for all drums
- **Per-Drum Error Handling**: Comprehensive error detection and recovery per drum
- **Critical Error Modal**: Persistent error display for backend/PLC failures requiring operator acknowledgment
- **Clear Error Flags**: Operator button to reset error flags and resume operations after issue resolution
- **Multi-Drum Progress Monitoring**: Real-time job status and progress tracking for all drums
- **Synchronized Safety Controls**: Emergency stop and error pause functionality affecting all drums
- **Material Synchronization**: Ensures all drums complete operations before proceeding

## Multi-Material Workflow

1. **Upload 3 CLI Files**: One file per drum (0, 1, 2) with potentially different layer counts
2. **Job Initialization**: Calculate total_layers as maximum among 3 files, prepare multi-drum coordination
3. **Layer Execution Loop**: 
   - Upload current layer to all 3 drums
   - Wait for all drums to be ready
   - Signal PLC to start coordinated print
   - Monitor all drums during printing
   - Wait for galvo completion
   - Progress to next layer
4. **Job Completion**: Final cleanup and status reporting

## Support

For implementation questions or issues, refer to:
- Testing guide for multi-drum validation procedures
- Code examples for 3-drum implementation patterns
- Architecture diagrams for multi-material system understanding
