@echo off
echo Starting Recoater Application in DEBUG mode...
echo Debug logs will be saved to logs directory

:: Create logs directory if it doesn't exist
if not exist logs mkdir logs

:: Clear previous log files
if exist logs\backend_debug.log del logs\backend_debug.log
if exist logs\frontend_debug.log del logs\frontend_debug.log

:: Start Backend in a new window with debug logging
start "Recoater Backend (DEBUG)" cmd /k "cd backend && python -m uvicorn app.main:app --reload --log-level debug > ..\logs\backend_debug.log 2>&1"

:: Wait for backend to start
timeout /t 5 /nobreak >nul

:: Start Frontend in a new window with logging
start "Recoater Frontend (DEBUG)" cmd /k "cd frontend && npm run dev > ..\logs\frontend_debug.log 2>&1"

:: Open browser after a short delay
timeout /t 5 /nobreak >nul
start "" http://localhost:5173

echo.
echo Application should be running at http://localhost:5173
echo Debug logs are being saved to:
echo - Backend: logs\backend_debug.log
echo - Frontend: logs\frontend_debug.log
echo.
echo You can monitor logs in real-time with:
echo   tail -f logs\backend_debug.log
echo   tail -f logs\frontend_debug.log
echo.
echo Press any key to exit this window (application will continue running)
pause
