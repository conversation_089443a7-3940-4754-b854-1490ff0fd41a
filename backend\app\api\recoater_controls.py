"""
Recoater Controls API Router
============================

This module provides the recoater drum control API endpoints for the Recoater HMI.
It handles requests for drum motion, ejection pressure, and suction pressure control.
"""

from fastapi import APIRouter, HTTPException, Depends, Path
from pydantic import BaseModel, Field
from typing import Dict, Any, Literal, Optional
import logging

from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError
from app.dependencies import get_recoater_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/recoater", tags=["recoater"])

# Pydantic models for request/response validation

class DrumMotionRequest(BaseModel):
    """Request model for drum motion commands."""
    mode: Literal["absolute", "relative", "turns", "speed", "homing"] = Field(..., description="Motion mode")
    speed: float = Field(..., gt=0, description="Movement speed in mm/s")
    distance: Optional[float] = Field(None, description="Distance to move in mm (for absolute/relative modes)")
    turns: Optional[float] = Field(None, description="Number of turns (for turns mode)")

class DrumEjectionRequest(BaseModel):
    """Request model for drum ejection pressure commands."""
    target: float = Field(..., ge=0, description="Target ejection pressure")
    unit: Literal["pascal", "bar"] = Field(default="pascal", description="Pressure unit")

class DrumSuctionRequest(BaseModel):
    """Request model for drum suction pressure commands."""
    target: float = Field(..., ge=0, description="Target suction pressure in Pa")

class BladeMotionRequest(BaseModel):
    """Request model for blade motion commands."""
    mode: Literal["absolute", "relative", "homing"] = Field(..., description="Motion mode")
    distance: Optional[float] = Field(None, description="Distance to move in µm (for absolute/relative modes)")

class BladeIndividualMotionRequest(BaseModel):
    """Request model for individual blade screw motion commands."""
    distance: float = Field(..., description="Relative distance of the motion [µm]")

class LevelerPressureRequest(BaseModel):
    """Request model for leveler pressure commands."""
    target: float = Field(..., ge=0, description="Target leveler pressure [Pa]")

# Drum Motion Endpoints

@router.get("/drums/{drum_id}/motion")
async def get_drum_motion(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current motion command for a drum.
    
    Args:
        drum_id: The drum's ID
        
    Returns:
        Dictionary containing current motion information
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting motion status for drum: {drum_id}")
        motion_data = client.get_drum_motion(drum_id)
        
        response = {
            "drum_id": drum_id,
            "motion": motion_data,
            "connected": True
        }
        
        logger.debug(f"Drum {drum_id} motion status retrieved successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/drums/{drum_id}/motion")
async def set_drum_motion(
    motion_request: DrumMotionRequest,
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Create a motion command for a drum.
    
    Args:
        drum_id: The drum's ID
        motion_request: Motion parameters (mode, speed, distance, turns)
        
    Returns:
        Dictionary containing motion command response
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting motion for drum {drum_id}: {motion_request}")
        
        response_data = client.set_drum_motion(
            drum_id=drum_id,
            mode=motion_request.mode,
            speed=motion_request.speed,
            distance=motion_request.distance,
            turns=motion_request.turns
        )
        
        response = {
            "drum_id": drum_id,
            "motion_command": motion_request.model_dump(),
            "response": response_data,
            "connected": True
        }
        
        logger.info(f"Drum {drum_id} motion command set successfully")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error setting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.delete("/drums/{drum_id}/motion")
async def cancel_drum_motion(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Cancel the current motion command for a drum.
    
    Args:
        drum_id: The drum's ID
        
    Returns:
        Dictionary containing cancellation response
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Cancelling motion for drum: {drum_id}")
        response_data = client.cancel_drum_motion(drum_id)
        
        response = {
            "drum_id": drum_id,
            "action": "motion_cancelled",
            "response": response_data,
            "connected": True
        }
        
        logger.info(f"Drum {drum_id} motion cancelled successfully")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error cancelling drum {drum_id} motion: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error cancelling drum {drum_id} motion: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error cancelling drum {drum_id} motion: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Drum Ejection Pressure Endpoints

@router.get("/drums/{drum_id}/ejection")
async def get_drum_ejection(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    unit: Literal["pascal", "bar"] = "pascal",
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the ejection pressure information for a drum.
    
    Args:
        drum_id: The drum's ID
        unit: Pressure unit ('pascal' or 'bar')
        
    Returns:
        Dictionary containing ejection pressure information
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting ejection pressure for drum {drum_id} in {unit}")
        ejection_data = client.get_drum_ejection(drum_id, unit)
        
        response = {
            "drum_id": drum_id,
            "ejection": ejection_data,
            "connected": True
        }
        
        logger.debug(f"Drum {drum_id} ejection pressure retrieved successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/drums/{drum_id}/ejection")
async def set_drum_ejection(
    ejection_request: DrumEjectionRequest,
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the target ejection pressure for a drum.
    
    Args:
        drum_id: The drum's ID
        ejection_request: Ejection pressure parameters (target, unit)
        
    Returns:
        Dictionary containing ejection pressure response
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting ejection pressure for drum {drum_id}: {ejection_request}")
        
        response_data = client.set_drum_ejection(
            drum_id=drum_id,
            target=ejection_request.target,
            unit=ejection_request.unit
        )
        
        response = {
            "drum_id": drum_id,
            "ejection_command": ejection_request.model_dump(),
            "response": response_data,
            "connected": True
        }
        
        logger.info(f"Drum {drum_id} ejection pressure set successfully")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error setting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Drum Suction Pressure Endpoints

@router.get("/drums/{drum_id}/suction")
async def get_drum_suction(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the suction pressure information for a drum.

    Args:
        drum_id: The drum's ID

    Returns:
        Dictionary containing suction pressure information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting suction pressure for drum: {drum_id}")
        suction_data = client.get_drum_suction(drum_id)

        response = {
            "drum_id": drum_id,
            "suction": suction_data,
            "connected": True
        }

        logger.debug(f"Drum {drum_id} suction pressure retrieved successfully: {response}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/drums/{drum_id}/suction")
async def set_drum_suction(
    suction_request: DrumSuctionRequest,
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the target suction pressure for a drum.

    Args:
        drum_id: The drum's ID
        suction_request: Suction pressure parameters (target)

    Returns:
        Dictionary containing suction pressure response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting suction pressure for drum {drum_id}: {suction_request}")

        response_data = client.set_drum_suction(
            drum_id=drum_id,
            target=suction_request.target
        )

        response = {
            "drum_id": drum_id,
            "suction_command": suction_request.model_dump(),
            "response": response_data,
            "connected": True
        }

        logger.info(f"Drum {drum_id} suction pressure set successfully")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error setting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Blade Control Endpoints

@router.get("/drums/{drum_id}/blade/screws")
async def get_blade_screws_info(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get information about the two screws of the specified drum's scraping blade.

    Args:
        drum_id: The drum's ID

    Returns:
        Dictionary containing blade screws information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting blade screws info for drum {drum_id}")

        response_data = client.get_blade_screws_info(drum_id)

        response = {
            "drum_id": drum_id,
            "blade_screws": response_data,
            "connected": True
        }

        logger.info(f"Blade screws info retrieved successfully for drum {drum_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting blade screws info for drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error getting blade screws info for drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting blade screws info for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/drums/{drum_id}/blade/screws/motion")
async def get_blade_screws_motion(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current motion command for the blade screws.

    Args:
        drum_id: The drum's ID

    Returns:
        Dictionary containing current motion information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting blade screws motion for drum {drum_id}")

        response_data = client.get_blade_screws_motion(drum_id)

        response = {
            "drum_id": drum_id,
            "motion": response_data,
            "connected": True
        }

        logger.info(f"Blade screws motion retrieved successfully for drum {drum_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error getting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/drums/{drum_id}/blade/screws/motion")
async def set_blade_screws_motion(
    motion_request: BladeMotionRequest,
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Create a motion command for the blade screws.

    Args:
        drum_id: The drum's ID
        motion_request: Motion parameters (mode, distance)

    Returns:
        Dictionary containing motion command response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting blade screws motion for drum {drum_id}: {motion_request}")

        response_data = client.set_blade_screws_motion(
            drum_id=drum_id,
            mode=motion_request.mode,
            distance=motion_request.distance
        )

        response = {
            "drum_id": drum_id,
            "motion_command": motion_request.model_dump(),
            "response": response_data,
            "connected": True
        }

        logger.info(f"Blade screws motion command set successfully for drum {drum_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error setting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error setting blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/drums/{drum_id}/blade/screws/motion")
async def cancel_blade_screws_motion(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Cancel the current motion command for the blade screws.

    Args:
        drum_id: The drum's ID

    Returns:
        Dictionary containing cancellation response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Cancelling blade screws motion for drum {drum_id}")

        response_data = client.cancel_blade_screws_motion(drum_id)

        response = {
            "drum_id": drum_id,
            "action": "motion_cancelled",
            "response": response_data,
            "connected": True
        }

        logger.info(f"Blade screws motion cancelled successfully for drum {drum_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error cancelling blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error cancelling blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error cancelling blade screws motion for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/drums/{drum_id}/blade/screws/{screw_id}")
async def get_blade_screw_info(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    screw_id: int = Path(..., ge=0, description="The screw's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get information about a specific screw of the drum's scraping blade.

    Args:
        drum_id: The drum's ID
        screw_id: The screw's ID

    Returns:
        Dictionary containing blade screw information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting blade screw info for drum {drum_id}, screw {screw_id}")

        response_data = client.get_blade_screw_info(drum_id, screw_id)

        response = {
            "drum_id": drum_id,
            "screw_id": screw_id,
            "screw_info": response_data,
            "connected": True
        }

        logger.info(f"Blade screw info retrieved successfully for drum {drum_id}, screw {screw_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting blade screw info for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error getting blade screw info for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting blade screw info for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/drums/{drum_id}/blade/screws/{screw_id}/motion")
async def get_blade_screw_motion(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    screw_id: int = Path(..., ge=0, description="The screw's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current motion command for a specific blade screw.

    Args:
        drum_id: The drum's ID
        screw_id: The screw's ID

    Returns:
        Dictionary containing current motion information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting blade screw motion for drum {drum_id}, screw {screw_id}")

        response_data = client.get_blade_screw_motion(drum_id, screw_id)

        response = {
            "drum_id": drum_id,
            "screw_id": screw_id,
            "motion": response_data,
            "connected": True
        }

        logger.info(f"Blade screw motion retrieved successfully for drum {drum_id}, screw {screw_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error getting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/drums/{drum_id}/blade/screws/{screw_id}/motion")
async def set_blade_screw_motion(
    motion_request: BladeIndividualMotionRequest,
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    screw_id: int = Path(..., ge=0, description="The screw's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Create a motion command for a specific blade screw.

    Args:
        drum_id: The drum's ID
        screw_id: The screw's ID
        motion_request: Motion parameters (distance)

    Returns:
        Dictionary containing motion command response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting blade screw motion for drum {drum_id}, screw {screw_id}: {motion_request}")

        response_data = client.set_blade_screw_motion(
            drum_id=drum_id,
            screw_id=screw_id,
            distance=motion_request.distance
        )

        response = {
            "drum_id": drum_id,
            "screw_id": screw_id,
            "motion_command": motion_request.model_dump(),
            "response": response_data,
            "connected": True
        }

        logger.info(f"Blade screw motion command set successfully for drum {drum_id}, screw {screw_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error setting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error setting blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/drums/{drum_id}/blade/screws/{screw_id}/motion")
async def cancel_blade_screw_motion(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    screw_id: int = Path(..., ge=0, description="The screw's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Cancel the current motion command for a specific blade screw.

    Args:
        drum_id: The drum's ID
        screw_id: The screw's ID

    Returns:
        Dictionary containing cancellation response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Cancelling blade screw motion for drum {drum_id}, screw {screw_id}")

        response_data = client.cancel_blade_screw_motion(drum_id, screw_id)

        response = {
            "drum_id": drum_id,
            "screw_id": screw_id,
            "action": "motion_cancelled",
            "response": response_data,
            "connected": True
        }

        logger.info(f"Blade screw motion cancelled successfully for drum {drum_id}, screw {screw_id}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error cancelling blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=503, detail="Failed to connect to recoater hardware")
    except RecoaterAPIError as e:
        logger.error(f"API error cancelling blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error cancelling blade screw motion for drum {drum_id}, screw {screw_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Leveler Control Endpoints

@router.get("/leveler/pressure")
async def get_leveler_pressure(
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the leveler pressure information.

    Returns:
        Dictionary containing leveler pressure information (maximum, target, value)

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info("Getting leveler pressure")
        pressure_data = client.get_leveler_pressure()

        response = {
            "leveler_pressure": pressure_data,
            "connected": True
        }

        logger.debug(f"Leveler pressure retrieved successfully: {response}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting leveler pressure: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting leveler pressure: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting leveler pressure: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/leveler/pressure")
async def set_leveler_pressure(
    pressure_request: LevelerPressureRequest,
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the target pressure for the leveler.

    Args:
        pressure_request: Leveler pressure parameters (target)

    Returns:
        Dictionary containing leveler pressure response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting leveler pressure: {pressure_request}")

        response_data = client.set_leveler_pressure(
            target=pressure_request.target
        )

        response = {
            "leveler_command": pressure_request.model_dump(),
            "response": response_data,
            "connected": True
        }

        logger.info("Leveler pressure set successfully")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting leveler pressure: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting leveler pressure: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error setting leveler pressure: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/leveler/sensor")
async def get_leveler_sensor(
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current state of the magnetic sensor on the leveler.

    Returns:
        Dictionary containing leveler sensor state

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info("Getting leveler sensor state")
        sensor_data = client.get_leveler_sensor()

        response = {
            "leveler_sensor": sensor_data,
            "connected": True
        }

        logger.debug(f"Leveler sensor state retrieved successfully: {response}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting leveler sensor: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting leveler sensor: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting leveler sensor: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
