# Project Context: Multi-Layer Print Job Implementation

## Background

### Current System Overview

The Recoater HMI system is a web-based interface for controlling Aerosint multi-component powder recoater hardware. The system consists of:

1. **Vue.js Frontend** - Touch-friendly operator interface
2. **FastAPI Backend** - REST API and WebSocket server
3. **Aerosint Server** - Hardware control API (external system)
4. **TwinCAT PLC** - Physical motion control and coordination

### Current Workflow Limitations

**Manual Process (Current State):**
1. Operator uploads 3 CLI files via web interface (one for each drum/material)
2. System uploads single layer from each CLI file to corresponding recoater drum (3 drums total)
3. Operator manually presses "Execute" on HMI client
4. P<PERSON> executes recoater movement for layer deposition from all 3 drums
5. After completion, operator manually presses "Move to Start Position"
6. **Problem**: CLI files revert to layer 1 - no automatic layer progression across any drums
7. Process must be repeated manually for each layer across all 3 drums

**Multi-Material System Complexity:**
- **3 Drum Configuration**: The system uses 3 separate drums for multi-material printing
- **Coordinated Operation**: All 3 drums must be loaded with corresponding layer data before each layer
- **Synchronization Required**: All drums must complete upload and be ready before print execution
- **Material Coordination**: Each layer requires precise coordination between different materials

**Critical Issue:**
The current system requires manual intervention for each layer across all 3 drums, making multi-material prints extremely labor-intensive and error-prone. The lack of automatic layer progression means operators must manually coordinate 3 separate material streams for every single layer.

## Problem Statement

### Primary Challenge
The recoater system can only process one layer at a time across 3 drums and requires manual coordination between multiple components:
- **Aerosint Server**: Manages print jobs for 3 drums, provides "ready"/"printing" status per drum
- **TwinCAT PLC**: Controls physical hardware movement and timing for 3-drum coordination
- **MCP Server**: Manages galvo/scanhead operations and completion signals
- **FastAPI Backend**: Coordinates between systems but lacks multi-material logic

### Multi-Material Coordination Requirements
1. **3-File Upload**: Accept and validate 3 CLI files (one per drum) with same layer count
2. **Layer Sequencing**: Automatically progress through all layers across all 3 drums simultaneously
3. **State Synchronization**: Ensure all 3 drums are ready before proceeding to next operation
4. **Error Handling**: Pause operations when any drum fails, handle partial failures
5. **Material Coordination**: Coordinate timing and execution across different material streams
6. **Safety Controls**: Allow operator intervention when needed across all drums

### Technical Constraints
- Cannot modify Aerosint server (external system)
- PLC must remain the primary hardware coordinator for all 3 drums
- Must maintain existing single-layer functionality
- System must be robust against component failures in any drum
- All 3 drums must be synchronized for each layer operation

**Critical AI Implementation Constraints:**
- **Exactly 3 Drums**: Hardware has drums 0, 1, 2 only - validate this in all UI/API components
- **ASCII CLI Required**: Hardware only accepts ASCII CLI format, not binary
- **No Direct MCP Communication**: All galvo control goes through PLC, not direct API calls
- **Sequential Layer Processing**: Cannot skip layers or process layers out of order
- **Synchronous Drum Operations**: All 3 drums must complete current layer before proceeding to next

## Requirements Analysis

### Functional Requirements

**FR-1: Automated Multi-Material Layer Progression**
- Upload 3 CLI files with N layers each (one per drum)
- Validate all 3 files have identical layer counts
- Automatically send layer 1, 2, 3... N to all 3 drums without manual intervention
- Coordinate layer progression across all drums simultaneously
- Remove completed layers from processing queue for all drums

**FR-2: Multi-Drum Component Coordination**
- Coordinate between Aerosint server (3 drums), PLC, and MCP server
- Ensure all 3 drums are ready before starting each layer
- Wait for all drums to complete before proceeding to next layer
- Maintain state consistency across all systems and drums

**FR-3: Real-time Multi-Drum Status Monitoring**
- Display current layer number and total progress for all drums
- Show waiting states per drum (recoater ready, uploading, etc.)
- Provide real-time feedback to operators for each drum
- Visual indication of drum synchronization status

**FR-4: Multi-Drum Error Handling and Recovery**
- Pause all operations when any drum encounters errors
- Display clear error messages per drum to operators
- Allow manual error clearing per drum and coordinated job resumption
- Handle partial failures (some drums succeed, others fail)

**FR-5: Multi-Material Job Control Operations**
- Start multi-material print jobs with 3 CLI files
- Cancel jobs affecting all drums
- Monitor job status across all drums
- Provide per-drum status and control

### Non-Functional Requirements

**NFR-1: Reliability**
- System must handle network interruptions gracefully
- Robust error detection and recovery mechanisms
- No data loss during component failures

**NFR-2: Performance**
- Real-time communication (< 1 second latency)
- Efficient OPC UA communication with PLC
- Minimal impact on existing system performance

**NFR-3: Safety**
- Emergency stop functionality
- Operator override capabilities
- Clear error indication and recovery procedures

**NFR-4: Maintainability**
- Clear separation of concerns between components
- Comprehensive logging for debugging
- Well-documented interfaces and protocols

## System Integration Points

### Aerosint Server Integration
- **API Endpoint**: `http://*************:8080`
- **Key States**: "ready", "printing", "error"
- **Critical Operations**:
  - Upload geometry to drum: `PUT /drums/{id}/geometry`
  - Start print job: `POST /print/job`
  - Get status: `GET /state`

### TwinCAT PLC Integration
- **Protocol**: OPC UA Server
- **Endpoint**: `opc.tcp://localhost:4840`
- **Role**: Primary coordinator for hardware timing
- **Responsibilities**:
  - Monitor recoater ready signals
  - Control physical movement timing
  - Coordinate with MCP server for galvo operations

### MCP Server Integration
- **Function**: Galvo/scanhead control
- **Communication**: Via PLC (exact protocol Unknown)
- **Signal**: galvo_scan_complete when layer scanning finished

**AI Implementation Note**: The MCP server communication is handled entirely by the PLC. AI agents should only monitor the `galvo_scan_complete` OPC UA variable and not attempt direct MCP communication.

## Current Architecture Analysis

### Existing Components

**FastAPI Backend (`backend/app/api/print.py`)**
- Single-layer CLI upload and processing
- Basic print job management
- CLI file parsing and temporary storage
- Integration with RecoaterClient

**CLI File Management**
- In-memory cache for parsed CLI files
- Temporary file generation for verification
- Layer extraction and ASCII CLI generation

**Recoater Client (`services/recoater_client.py`)**
- HTTP client for Aerosint server communication
- Error handling and retry logic
- Print job status monitoring

### Missing Components

**Multi-Layer Job State Management**
- No persistent job state across layers
- No layer progression tracking
- No coordination state management

**OPC UA Communication**
- No PLC integration
- No real-time coordination capability
- No shared state variables

**Error Recovery Mechanisms**
- Limited error handling for multi-component failures
- No pause/resume functionality
- No operator intervention capabilities

## Implementation Strategy

### Approach: Incremental Enhancement
Rather than rebuilding the system, we will extend existing functionality:

1. **Preserve Existing Functionality**: All current single-layer operations remain unchanged
2. **Add New Capabilities**: Implement multi-layer as additional endpoints
3. **Minimal Disruption**: Use existing CLI parsing and recoater communication
4. **Clear Separation**: New multi-layer logic in separate modules

### Key Design Decisions

**Backend Authority for Layer Management**
- FastAPI backend manages layer sequencing and CLI file processing
- Backend monitors Aerosint status to detect layer completion
- Reduces complexity and provides single source of truth

**PLC as Hardware Coordinator**
- PLC remains responsible for physical timing and movement
- PLC coordinates with MCP server for galvo operations
- Maintains safety and timing critical functions

**OPC UA for Real-time Communication**
- Bidirectional communication between backend and PLC
- Event-driven coordination using OPC UA subscriptions
- Standardized industrial protocol for reliability

**Event-Driven Architecture**
- State changes trigger automatic progressions
- Asynchronous processing prevents blocking
- Clear handshake protocols between components

## Success Criteria

### Primary Success Metrics
1. **Automated Operation**: Complete N-layer print without manual intervention
2. **Coordination Accuracy**: 100% success rate for layer transitions
3. **Error Recovery**: System pauses safely on any component error
4. **Operator Visibility**: Real-time status and progress information

### Secondary Success Metrics
1. **Performance**: Layer transition time < 30 seconds
2. **Reliability**: 99%+ uptime during multi-layer operations
3. **Usability**: Intuitive operator interface for job management
4. **Maintainability**: Clear logs and diagnostic information

## Risk Analysis

### Technical Risks
- **OPC UA Communication Failures**: Mitigation via connection monitoring and retries
- **State Synchronization Issues**: Mitigation via comprehensive state validation
- **Aerosint Server Reliability**: Mitigation via status polling and timeout handling

### Operational Risks
- **Operator Training**: New interface requires operator familiarization
- **System Complexity**: Additional coordination increases failure points
- **Hardware Dependencies**: More components means more potential failures

### Mitigation Strategies
- Comprehensive testing at each implementation stage
- Fallback to manual operation when automation fails
- Clear error messages and recovery procedures
- Extensive logging for troubleshooting

## Next Steps

1. **Review Architecture Design** - Understand system components and interactions
2. **Plan Implementation Stages** - Break down work into manageable phases
3. **Prepare Development Environment** - Set up OPC UA and testing infrastructure
4. **Begin Stage 1 Implementation** - Start with OPC UA infrastructure
