import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import PrintView from '../src/views/PrintView.vue'
import * as apiService from '../src/services/api.js'

// Mock the API service
vi.mock('../src/services/api.js', () => {
  const mockFns = {
    uploadCliFile: vi.fn(),
    getCliLayerPreview: vi.fn(),
    sendCliLayerToDrum: vi.fn(),
    startPrintJob: vi.fn(),
    cancelPrintJob: vi.fn(),
    setLayerParameters: vi.fn(),
    getLayerParameters: vi.fn().mockResolvedValue({
      data: {
        filling_id: 0,
        patterning_speed_mm_s: 10,
        layer_height_um: 50,
        collector_position_mm: 1.0,
        leveler_pressure_bar: 2.0
      }
    }),
    getLayerPreview: vi.fn(),
    uploadDrumGeometry: vi.fn(),
    downloadDrumGeometry: vi.fn(),
    deleteDrumGeometry: vi.fn()
  }
  
  return {
    default: mockFns,
    ...mockFns
  }
})

// Mock the status store
const mockStatusStore = {
  isConnected: true,
  connectWebSocket: vi.fn(),
  disconnectWebSocket: vi.fn()
}

vi.mock('../src/stores/status.js', () => ({
  useStatusStore: () => mockStatusStore
}))

describe('PrintView CLI Layer Sending', () => {
  let wrapper

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    wrapper = mount(PrintView, {
      global: {
        stubs: {
          'router-link': true
        }
      }
    })
  })

  it('renders CLI layer preview section when file is uploaded', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 5
    }
    await wrapper.vm.$nextTick()

    expect(wrapper.find('.layer-preview-section').exists()).toBe(true)
    expect(wrapper.text()).toContain('Total Layers: 5')
  })

  it('shows target drum selection when CLI file is uploaded', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 3
    }
    await wrapper.vm.$nextTick()

    const drumSelect = wrapper.find('#target-drum-select')
    expect(drumSelect.exists()).toBe(true)
    expect(drumSelect.findAll('option')).toHaveLength(4) // Empty option + 3 drums
  })

  it('shows send layer button when CLI file is uploaded', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 2
    }
    await wrapper.vm.$nextTick()

    // Look for the send button by text content
    const buttons = wrapper.findAll('button')
    const sendButton = buttons.find(btn => btn.text().includes('Send Layer to Recoater'))
    expect(sendButton).toBeDefined()
    expect(sendButton.text()).toContain('Send Layer to Recoater')
  })

  it('disables send button when no drum is selected', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 2
    }
    wrapper.vm.selectedLayerNum = 1
    wrapper.vm.selectedTargetDrumId = ''
    await wrapper.vm.$nextTick()

    const buttons = wrapper.findAll('button')
    const sendButton = buttons.find(btn => btn.text().includes('Send Layer to Recoater'))
    expect(sendButton).toBeDefined()
    expect(sendButton.attributes('disabled')).toBeDefined()
  })

  it('enables send button when all required fields are filled', async () => {
    // Simulate CLI file upload
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 2
    }
    wrapper.vm.selectedLayerNum = 1
    wrapper.vm.selectedTargetDrumId = '1'
    await wrapper.vm.$nextTick()

    const buttons = wrapper.findAll('button')
    const sendButton = buttons.find(btn => btn.text().includes('Send Layer to Recoater'))
    expect(sendButton).toBeDefined()
    expect(sendButton.attributes('disabled')).toBeUndefined()
  })

  it('calls API service when send layer button is clicked', async () => {
    // Mock successful API response
    apiService.sendCliLayerToDrum.mockResolvedValue({
      data: {
        success: true,
        message: 'Layer sent successfully'
      }
    })

    // Setup component state
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 3
    }
    wrapper.vm.selectedLayerNum = 2
    wrapper.vm.selectedTargetDrumId = '1'
    await wrapper.vm.$nextTick()

    // Click send button
    const sendButton = wrapper.find('[data-testid="send-layer-button"]')
    if (!sendButton.exists()) {
      // Fallback to class-based selector
      const buttons = wrapper.findAll('button')
      const sendButtonAlt = buttons.find(btn => btn.text().includes('Send Layer'))
      expect(sendButtonAlt).toBeDefined()
      await sendButtonAlt.trigger('click')
    } else {
      await sendButton.trigger('click')
    }
    
    await wrapper.vm.$nextTick()

    // Verify API was called with correct parameters
    expect(apiService.sendCliLayerToDrum).toHaveBeenCalledWith('test-file-id', 2, 1)
  })

  it('shows loading state when sending layer', async () => {
    // Mock API to return a pending promise
    let resolvePromise
    const pendingPromise = new Promise(resolve => {
      resolvePromise = resolve
    })
    apiService.sendCliLayerToDrum.mockReturnValue(pendingPromise)

    // Setup component state
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 2
    }
    wrapper.vm.selectedLayerNum = 1
    wrapper.vm.selectedTargetDrumId = '0'
    await wrapper.vm.$nextTick()

    // Click send button
    const buttons = wrapper.findAll('button')
    const sendButton = buttons.find(btn => btn.text().includes('Send Layer to Recoater'))
    expect(sendButton).toBeDefined()
    
    await sendButton.trigger('click')
    await wrapper.vm.$nextTick()

    // Check loading state
    expect(wrapper.vm.isCliSendLoading).toBe(true)
    const loadingButton = buttons.find(btn => btn.text().includes('Sending...'))
    expect(loadingButton).toBeDefined()

    // Resolve the promise
    resolvePromise({ data: { success: true } })
    
    // Wait for the promise to resolve and Vue to update
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0)) // Allow microtasks to complete
    await wrapper.vm.$nextTick()

    // Check loading state is cleared
    expect(wrapper.vm.isCliSendLoading).toBe(false)
  })

  it('handles API errors when sending layer', async () => {
    // Mock API error
    const errorResponse = {
      response: {
        data: {
          detail: 'Connection error'
        }
      }
    }
    apiService.sendCliLayerToDrum.mockRejectedValue(errorResponse)

    // Setup component state
    wrapper.vm.cliFileInfo = {
      file_id: 'test-file-id',
      total_layers: 1
    }
    wrapper.vm.selectedLayerNum = 1
    wrapper.vm.selectedTargetDrumId = '2'
    await wrapper.vm.$nextTick()

    // Click send button
    const buttons = wrapper.findAll('button')
    const sendButton = buttons.find(btn => btn.text().includes('Send Layer to Recoater'))
    expect(sendButton).toBeDefined()
    
    await sendButton.trigger('click')
    await wrapper.vm.$nextTick()

    // Wait for error handling
    await wrapper.vm.$nextTick()

    // Verify error handling
    expect(wrapper.vm.isCliSendLoading).toBe(false)
    // Note: We can't easily test the showMessage call without more complex mocking
  })
})
