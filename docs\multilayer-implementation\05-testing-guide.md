# Testing Guide: Multi-Layer Print Job System

## Overview

This document provides comprehensive testing procedures for validating the multi-layer print job system. Testing is organized by implementation stage and includes unit tests, integration tests, and end-to-end validation scenarios.

## Testing Strategy

### Testing Pyramid

```
     /\
    /  \     End-to-End Tests (E2E)
   /____\    - Complete workflow validation
  /      \   - Error scenario testing
 /        \  - Performance validation
/__________\ 

    /\       Integration Tests
   /  \      - Component interaction
  /____\     - API endpoint testing
 /      \    - OPC UA communication
/__________\  

      /\     Unit Tests
     /  \    - Individual function testing
    /____\   - Model validation
   /      \  - Utility function testing
  /__________\
```

### Testing Tools and Frameworks

**Backend Testing:**
- **pytest**: Python testing framework
- **pytest-asyncio**: Async testing support
- **httpx**: HTTP client for API testing
- **pytest-mock**: Mocking framework

**Frontend Testing:**
- **Vitest**: Vue.js testing framework
- **@vue/test-utils**: Vue component testing
- **jsdom**: DOM simulation
- **MSW**: Mock Service Worker for API mocking

**Integration Testing:**
- **Docker Compose**: Complete system setup
- **OPC UA Test Server**: PLC simulation
- **Mock Aerosint Server**: Hardware simulation

## Stage 1 Testing: OPC UA Infrastructure

### Unit Tests

#### Test File: `backend/tests/test_opcua_coordinator.py`

```python
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
from app.services.opcua_coordinator import OPCUACoordinator
from app.config.opcua_config import OPCUAConfig

@pytest.fixture
def opcua_config():
    """Create test OPC UA configuration"""
    return OPCUAConfig(
        endpoint="opc.tcp://localhost:4840",
        namespace=2,
        connection_timeout=1.0
    )

@pytest.fixture
def opcua_coordinator(opcua_config):
    """Create OPC UA coordinator for testing"""
    return OPCUACoordinator(opcua_config)

@pytest.mark.asyncio
async def test_opcua_connection_success(opcua_coordinator):
    """Test successful OPC UA connection"""
    # Mock the OPC UA client
    mock_client = AsyncMock()
    mock_node = AsyncMock()
    
    with pytest.patch('app.services.opcua_coordinator.Client', return_value=mock_client):
        mock_client.connect = AsyncMock()
        mock_client.get_node = AsyncMock(return_value=mock_node)
        mock_client.create_subscription = AsyncMock()
        
        # Test connection
        result = await opcua_coordinator.connect()
        
        assert result is True
        assert opcua_coordinator.is_connected() is True
        mock_client.connect.assert_called_once()

@pytest.mark.asyncio
async def test_opcua_connection_failure(opcua_coordinator):
    """Test OPC UA connection failure"""
    with pytest.patch('app.services.opcua_coordinator.Client') as mock_client_class:
        mock_client = AsyncMock()
        mock_client.connect.side_effect = Exception("Connection failed")
        mock_client_class.return_value = mock_client
        
        # Test connection failure
        result = await opcua_coordinator.connect()
        
        assert result is False
        assert opcua_coordinator.is_connected() is False

@pytest.mark.asyncio
async def test_variable_read_write(opcua_coordinator):
    """Test OPC UA variable read/write operations"""
    # Setup mock client
    mock_client = AsyncMock()
    mock_node = AsyncMock()
    mock_node.read_value = AsyncMock(return_value=True)
    mock_node.write_value = AsyncMock()
    
    opcua_coordinator.client = mock_client
    opcua_coordinator._connected = True
    opcua_coordinator.nodes = {"test_var": mock_node}
    
    # Test write operation
    result = await opcua_coordinator.write_variable("test_var", True)
    assert result is True
    mock_node.write_value.assert_called_once_with(True)
    
    # Test read operation
    value = await opcua_coordinator.read_variable("test_var")
    assert value is True
    mock_node.read_value.assert_called_once()

@pytest.mark.asyncio
async def test_multiple_variable_write(opcua_coordinator):
    """Test batch variable write operations"""
    mock_client = AsyncMock()
    mock_node1 = AsyncMock()
    mock_node2 = AsyncMock()
    
    opcua_coordinator.client = mock_client
    opcua_coordinator._connected = True
    opcua_coordinator.nodes = {
        "var1": mock_node1,
        "var2": mock_node2
    }
    
    variables = {"var1": True, "var2": False}
    
    result = await opcua_coordinator.write_multiple_variables(variables)
    assert result is True
    mock_client.write_values.assert_called_once()

@pytest.mark.asyncio
async def test_subscription_handling(opcua_coordinator):
    """Test OPC UA subscription data change handling"""
    # Setup mock subscription
    mock_node = AsyncMock()
    opcua_coordinator.nodes = {"galvo_scan_complete": mock_node}
    
    # Simulate data change notification
    await opcua_coordinator._handle_data_change("galvo_scan_complete", True)
    
    # Verify event was processed (would need more specific assertions based on implementation)
    assert True  # Placeholder - add specific assertions based on your implementation

class TestOPCUAIntegration:
    """Integration tests with actual OPC UA server"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_opcua_connection(self):
        """Test connection to real OPC UA server (requires server running)"""
        config = OPCUAConfig(
            endpoint="opc.tcp://localhost:4840",
            connection_timeout=5.0
        )
        coordinator = OPCUACoordinator(config)
        
        try:
            result = await coordinator.connect()
            if result:
                # Test basic operations
                await coordinator.write_variable("job_active", False)
                value = await coordinator.read_variable("job_active")
                assert value is False
                
                await coordinator.disconnect()
        except Exception as e:
            pytest.skip(f"OPC UA server not available: {e}")
```

#### Test File: `backend/tests/test_opcua_config.py`

```python
import pytest
import os
from app.config.opcua_config import OPCUAConfig

def test_default_config():
    """Test default OPC UA configuration"""
    config = OPCUAConfig()
    
    assert config.endpoint == "opc.tcp://localhost:4840"
    assert config.namespace == 2
    assert config.connection_timeout == 5.0
    assert "job_active" in config.variable_nodes

def test_environment_config(monkeypatch):
    """Test configuration from environment variables"""
    monkeypatch.setenv("OPCUA_ENDPOINT", "opc.tcp://test:4841")
    monkeypatch.setenv("OPCUA_NAMESPACE", "3")
    monkeypatch.setenv("OPCUA_TIMEOUT", "10.0")
    
    config = OPCUAConfig.from_environment()
    
    assert config.endpoint == "opc.tcp://test:4841"
    assert config.namespace == 3
    assert config.connection_timeout == 10.0

def test_variable_node_generation():
    """Test variable node ID generation"""
    config = OPCUAConfig(namespace=5)
    
    expected_node = "ns=5;s=job_active"
    assert config.variable_nodes["job_active"] == expected_node
```

### Integration Tests

#### Test File: `backend/tests/integration/test_opcua_plc_integration.py`

```python
import pytest
import asyncio
from app.services.opcua_coordinator import opcua_coordinator

@pytest.mark.integration
@pytest.mark.asyncio
class TestOPCUAPLCIntegration:
    """Integration tests with PLC OPC UA server"""
    
    async def test_connection_lifecycle(self):
        """Test complete connection lifecycle"""
        # Connect
        connected = await opcua_coordinator.connect()
        assert connected, "Should connect to OPC UA server"
        
        # Test basic operations
        await opcua_coordinator.write_variable("job_active", False)
        value = await opcua_coordinator.read_variable("job_active")
        assert value is False
        
        # Test batch operations
        variables = {
            "job_active": True,
            "total_layers": 10,
            "current_layer": 1
        }
        result = await opcua_coordinator.write_multiple_variables(variables)
        assert result is True
        
        # Verify values
        job_active = await opcua_coordinator.read_variable("job_active")
        total_layers = await opcua_coordinator.read_variable("total_layers")
        current_layer = await opcua_coordinator.read_variable("current_layer")
        
        assert job_active is True
        assert total_layers == 10
        assert current_layer == 1
        
        # Reset and disconnect
        await opcua_coordinator.reset_coordination_flags()
        await opcua_coordinator.disconnect()
        
        assert not opcua_coordinator.is_connected()
    
    async def test_subscription_events(self):
        """Test OPC UA subscription functionality"""
        connected = await opcua_coordinator.connect()
        assert connected
        
        # Setup event handler
        events_received = []
        
        async def test_handler(value):
            events_received.append(value)
        
        opcua_coordinator.add_event_handler("galvo_scan_complete", test_handler)
        
        # Trigger event by writing to variable
        await opcua_coordinator.write_variable("galvo_scan_complete", True)
        
        # Wait for subscription notification
        await asyncio.sleep(0.5)
        
        # Clean up
        opcua_coordinator.remove_event_handler("galvo_scan_complete", test_handler)
        await opcua_coordinator.disconnect()
        
        # Note: This test may need adjustment based on actual PLC behavior
    
    async def test_error_handling(self):
        """Test error handling during OPC UA operations"""
        connected = await opcua_coordinator.connect()
        assert connected
        
        # Test invalid variable read/write
        result = await opcua_coordinator.write_variable("invalid_var", True)
        assert result is False
        
        value = await opcua_coordinator.read_variable("invalid_var")
        assert value is None
        
        await opcua_coordinator.disconnect()
```

---

## Stage 2 Testing: Multi-Layer Job Management

### Unit Tests

#### Test File: `backend/tests/test_multilayer_job.py`

```python
import pytest
import time
from app.models.multilayer_job import MultiLayerJobState, JobStatus, LayerData

def test_job_state_initialization():
    """Test job state initialization"""
    job_state = MultiLayerJobState()
    
    assert job_state.is_active is False
    assert job_state.status == JobStatus.IDLE
    assert job_state.current_layer == 0
    assert job_state.total_layers == 0

def test_progress_calculation():
    """Test progress percentage calculation"""
    job_state = MultiLayerJobState(
        total_layers=10,
        current_layer=5
    )
    
    progress = job_state.get_progress_percentage()
    assert progress == 40.0  # (5-1)/10 * 100

def test_time_estimation():
    """Test time estimation calculations"""
    job_state = MultiLayerJobState(
        total_layers=10,
        current_layer=3,
        start_time=time.time() - 120  # Started 2 minutes ago
    )
    
    estimated = job_state.get_estimated_time_remaining()
    assert estimated is not None
    assert estimated > 0

def test_job_state_validation():
    """Test job state validation methods"""
    job_state = MultiLayerJobState(
        total_layers=5,
        remaining_layers=[LayerData(1, 0.1, b'test')]
    )
    
    # Can start job
    assert job_state.can_start_job() is True
    
    # Activate job
    job_state.is_active = True
    job_state.status = JobStatus.WAITING_FOR_PRINT_START
    
    # Cannot start another job
    assert job_state.can_start_job() is False
    
    # Can cancel active job
    assert job_state.can_cancel_job() is True

def test_job_reset():
    """Test job state reset functionality"""
    job_state = MultiLayerJobState(
        total_layers=5,
        current_layer=3,
        is_active=True,
        status=JobStatus.WAITING_FOR_GALVO,
        error_message="Test error"
    )
    
    original_job_id = job_state.job_id
    job_state.reset_job()
    
    assert job_state.job_id != original_job_id
    assert job_state.current_layer == 0
    assert job_state.is_active is False
    assert job_state.status == JobStatus.IDLE
    assert job_state.error_message == ""
```

#### Test File: `backend/tests/test_multilayer_api.py`

```python
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
from app.main import app
from app.api.print import cli_file_cache
from services.cli_parser import ParsedCliFile

client = TestClient(app)

@pytest.fixture
def mock_cli_file():
    """Create mock CLI file data"""
    return ParsedCliFile(
        layers=[
            {"layer_number": 1, "z_height": 0.1},
            {"layer_number": 2, "z_height": 0.2},
            {"layer_number": 3, "z_height": 0.3}
        ],
        header_lines=["test header"],
        file_size=1000
    )

@pytest.fixture
def setup_cli_cache(mock_cli_file):
    """Setup CLI file cache for testing"""
    file_id = "test-file-123"
    cli_file_cache[file_id] = mock_cli_file
    yield file_id
    # Cleanup
    if file_id in cli_file_cache:
        del cli_file_cache[file_id]

class TestMultiLayerAPI:
    """Test multi-layer job API endpoints"""
    
    @patch('app.services.opcua_coordinator.opcua_coordinator')
    @patch('app.services.coordination_engine.coordination_engine')
    def test_start_multilayer_job_success(self, mock_coordination, mock_opcua, setup_cli_cache):
        """Test successful multi-layer job start"""
        file_id = setup_cli_cache
        
        # Mock OPC UA coordinator
        mock_opcua.is_connected.return_value = True
        mock_opcua.connect = AsyncMock(return_value=True)
        mock_opcua.write_multiple_variables = AsyncMock(return_value=True)
        mock_opcua.reset_coordination_flags = AsyncMock()
        
        # Mock coordination engine
        mock_coordination.start_multilayer_job = AsyncMock(return_value=True)
        
        # Make request
        response = client.post(
            f"/print/cli/{file_id}/start-multilayer-job",
            json={"drum_id": 0}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["file_id"] == file_id
        assert data["total_layers"] == 3
    
    def test_start_multilayer_job_file_not_found(self):
        """Test multi-layer job start with invalid file ID"""
        response = client.post(
            "/print/cli/invalid-file/start-multilayer-job",
            json={"drum_id": 0}
        )
        
        assert response.status_code == 404
        assert "CLI file not found" in response.json()["detail"]
    
    def test_get_multilayer_job_status(self):
        """Test job status endpoint"""
        response = client.get("/print/multilayer-job/status")
        
        assert response.status_code == 200
        data = response.json()
        assert "is_active" in data
        assert "progress_percentage" in data
        assert "status" in data
    
    @patch('app.services.opcua_coordinator.opcua_coordinator')
    def test_cancel_multilayer_job(self, mock_opcua, setup_cli_cache):
        """Test job cancellation"""
        # Setup active job state
        from app.models.multilayer_job import multilayer_job_state, JobStatus
        multilayer_job_state.is_active = True
        multilayer_job_state.status = JobStatus.WAITING_FOR_PRINT_START
        
        mock_opcua.is_connected.return_value = True
        mock_opcua.write_multiple_variables = AsyncMock(return_value=True)
        
        response = client.post("/print/multilayer-job/cancel")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        # Cleanup
        multilayer_job_state.reset_job()
    
    def test_cancel_multilayer_job_no_active_job(self):
        """Test job cancellation with no active job"""
        response = client.post("/print/multilayer-job/cancel")
        
        assert response.status_code == 400
        assert "No active job to cancel" in response.json()["detail"]
    
    @patch('app.services.opcua_coordinator.opcua_coordinator')
    def test_clear_system_error(self, mock_opcua):
        """Test system error clearing"""
        mock_opcua.is_connected.return_value = True
        mock_opcua.write_multiple_variables = AsyncMock(return_value=True)
        
        response = client.post("/print/multilayer-job/clear-error")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
```

---

## Stage 3 Testing: Coordination Logic

### Integration Tests

#### Test File: `backend/tests/integration/test_coordination_engine.py`

```python
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from app.services.coordination_engine import coordination_engine
from app.models.multilayer_job import MultiLayerJobState, JobStatus, LayerData

@pytest.fixture
def sample_job_state():
    """Create sample job state for testing"""
    job_state = MultiLayerJobState()
    job_state.file_id = "test-file-123"
    job_state.total_layers = 3
    job_state.current_layer = 1
    job_state.drum_id = 0
    job_state.remaining_layers = [
        LayerData(1, 0.1, b'layer1'),
        LayerData(2, 0.2, b'layer2'),
        LayerData(3, 0.3, b'layer3')
    ]
    return job_state

@pytest.mark.asyncio
class TestCoordinationEngine:
    """Test coordination engine functionality"""
    
    @patch('app.services.opcua_coordinator.opcua_coordinator')
    @patch('app.dependencies.get_recoater_client')
    async def test_start_multilayer_job(self, mock_recoater, mock_opcua, sample_job_state):
        """Test multi-layer job initialization"""
        # Mock recoater client
        mock_client = MagicMock()
        mock_client.upload_drum_geometry.return_value = {"success": True}
        mock_client.start_print_job.return_value = {"success": True}
        mock_recoater.return_value = mock_client
        
        # Mock OPC UA coordinator
        mock_opcua.write_variable = AsyncMock(return_value=True)
        
        # Start job
        result = await coordination_engine.start_multilayer_job(sample_job_state)
        
        assert result is True
        assert sample_job_state.status == JobStatus.WAITING_FOR_PRINT_START
        mock_client.upload_drum_geometry.assert_called_once()
        mock_client.start_print_job.assert_called_once()
    
    @patch('app.services.opcua_coordinator.opcua_coordinator')
    @patch('app.dependencies.get_recoater_client')
    async def test_layer_progression_cycle(self, mock_recoater, mock_opcua, sample_job_state):
        """Test complete layer progression cycle"""
        # Setup mocks
        mock_client = MagicMock()
        mock_client.get_print_job_status.side_effect = [
            {"state": "printing"},  # First status check
            {"state": "ready"}      # Second status check (layer complete)
        ]
        mock_client.upload_drum_geometry.return_value = {"success": True}
        mock_client.start_print_job.return_value = {"success": True}
        mock_recoater.return_value = mock_client
        
        mock_opcua.write_variable = AsyncMock(return_value=True)
        mock_opcua.read_variable = AsyncMock(return_value=True)
        
        # Setup job state
        sample_job_state.is_active = True
        sample_job_state.status = JobStatus.WAITING_FOR_PRINT_START
        
        # Test status monitoring
        await coordination_engine._monitor_aerosint_status(sample_job_state)
        
        # Verify state transitions
        mock_opcua.write_variable.assert_any_call("recoater_ready_to_print", True)
        mock_opcua.write_variable.assert_any_call("recoater_layer_complete", True)
    
    @patch('app.services.opcua_coordinator.opcua_coordinator')
    async def test_galvo_completion_handling(self, mock_opcua, sample_job_state):
        """Test galvo completion event handling"""
        mock_opcua.write_variable = AsyncMock(return_value=True)
        
        # Setup job state
        sample_job_state.is_active = True
        sample_job_state.current_layer = 2
        sample_job_state.waiting_for_galvo = True
        
        # Simulate galvo completion
        await coordination_engine.handle_galvo_completion(sample_job_state)
        
        # Verify layer increment
        assert sample_job_state.current_layer == 3
        assert sample_job_state.waiting_for_galvo is False
        mock_opcua.write_variable.assert_called_with("current_layer", 3)
    
    @patch('app.services.opcua_coordinator.opcua_coordinator')
    async def test_job_completion(self, mock_opcua, sample_job_state):
        """Test job completion handling"""
        mock_opcua.write_variable = AsyncMock(return_value=True)
        
        # Setup job state for completion
        sample_job_state.is_active = True
        sample_job_state.current_layer = 4  # Beyond total layers
        
        # Complete job
        await coordination_engine.complete_job(sample_job_state)
        
        # Verify job completion
        assert sample_job_state.is_active is False
        assert sample_job_state.status == JobStatus.JOB_COMPLETE
        mock_opcua.write_variable.assert_called_with("job_active", False)
    
    @patch('app.services.opcua_coordinator.opcua_coordinator')
    async def test_error_handling(self, mock_opcua, sample_job_state):
        """Test error handling during coordination"""
        mock_opcua.write_variable = AsyncMock(return_value=True)
        
        error_message = "Test error condition"
        
        # Handle error
        await coordination_engine.handle_error(sample_job_state, error_message)
        
        # Verify error state
        assert sample_job_state.is_active is False
        assert sample_job_state.status == JobStatus.ERROR
        assert sample_job_state.error_message == error_message
        
        mock_opcua.write_variable.assert_any_call("system_error", True)
        mock_opcua.write_variable.assert_any_call("error_message", error_message)
```

### End-to-End Tests

#### Test File: `backend/tests/e2e/test_complete_workflow.py`

```python
import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch
from app.main import app
from app.api.print import cli_file_cache
from services.cli_parser import ParsedCliFile

@pytest.mark.e2e
@pytest.mark.asyncio
class TestCompleteWorkflow:
    """End-to-end testing of complete multi-layer workflow"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def mock_complete_system(self):
        """Mock all external systems for E2E testing"""
        with patch('app.services.opcua_coordinator.opcua_coordinator') as mock_opcua, \
             patch('app.dependencies.get_recoater_client') as mock_recoater, \
             patch('app.services.coordination_engine.coordination_engine') as mock_coordination:
            
            # Setup OPC UA mock
            mock_opcua.is_connected.return_value = True
            mock_opcua.connect = AsyncMock(return_value=True)
            mock_opcua.disconnect = AsyncMock()
            mock_opcua.write_variable = AsyncMock(return_value=True)
            mock_opcua.write_multiple_variables = AsyncMock(return_value=True)
            mock_opcua.read_variable = AsyncMock(return_value=False)
            mock_opcua.reset_coordination_flags = AsyncMock()
            
            # Setup Recoater client mock
            mock_client = MagicMock()
            mock_client.upload_drum_geometry.return_value = {"success": True}
            mock_client.start_print_job.return_value = {"success": True}
            mock_client.get_print_job_status.return_value = {"state": "ready"}
            mock_client.cancel_print_job.return_value = {"success": True}
            mock_recoater.return_value = mock_client
            
            # Setup coordination engine mock
            mock_coordination.start_multilayer_job = AsyncMock(return_value=True)
            mock_coordination.handle_galvo_completion = AsyncMock()
            mock_coordination.complete_job = AsyncMock()
            mock_coordination.handle_error = AsyncMock()
            
            yield {
                'opcua': mock_opcua,
                'recoater': mock_client,
                'coordination': mock_coordination
            }
    
    async def test_complete_multilayer_workflow(self, client, mock_complete_system):
        """Test complete multi-layer print workflow"""
        
        # Setup test CLI file
        test_file = ParsedCliFile(
            layers=[
                {"layer_number": 1, "z_height": 0.1},
                {"layer_number": 2, "z_height": 0.2}
            ],
            header_lines=["test header"],
            file_size=500
        )
        file_id = "test-workflow-file"
        cli_file_cache[file_id] = test_file
        
        try:
            # Step 1: Start multi-layer job
            response = client.post(
                f"/print/cli/{file_id}/start-multilayer-job",
                json={"drum_id": 0}
            )
            
            assert response.status_code == 200
            job_data = response.json()
            assert job_data["success"] is True
            assert job_data["total_layers"] == 2
            
            # Step 2: Check initial job status
            response = client.get("/print/multilayer-job/status")
            assert response.status_code == 200
            status = response.json()
            assert status["is_active"] is True
            assert status["current_layer"] == 1
            
            # Step 3: Simulate layer progression
            # This would normally be triggered by OPC UA events
            # For testing, we simulate the progression
            
            # Simulate first layer completion
            mock_complete_system['opcua'].datachange_notification(
                None, True, None  # galvo_scan_complete = True
            )
            
            # Allow async processing
            await asyncio.sleep(0.1)
            
            # Step 4: Check status after layer completion
            response = client.get("/print/multilayer-job/status")
            status = response.json()
            # Note: Actual layer increment would depend on coordination engine
            
            # Step 5: Cancel job
            response = client.post("/print/multilayer-job/cancel")
            assert response.status_code == 200
            
            # Step 6: Verify cancellation
            response = client.get("/print/multilayer-job/status")
            status = response.json()
            assert status["is_active"] is False
            
        finally:
            # Cleanup
            if file_id in cli_file_cache:
                del cli_file_cache[file_id]
    
    async def test_error_recovery_workflow(self, client, mock_complete_system):
        """Test error handling and recovery workflow"""
        
        # Setup test CLI file
        test_file = ParsedCliFile(
            layers=[{"layer_number": 1, "z_height": 0.1}],
            header_lines=["test header"],
            file_size=100
        )
        file_id = "test-error-file"
        cli_file_cache[file_id] = test_file
        
        try:
            # Start job
            response = client.post(
                f"/print/cli/{file_id}/start-multilayer-job",
                json={"drum_id": 0}
            )
            assert response.status_code == 200
            
            # Simulate error condition
            mock_complete_system['opcua'].read_variable.return_value = True  # system_error = True
            mock_complete_system['opcua'].read_variable.return_value = "Test error message"
            
            # Check error status
            response = client.get("/print/multilayer-job/status")
            status = response.json()
            # Note: Error detection would depend on actual implementation
            
            # Clear error
            response = client.post("/print/multilayer-job/clear-error")
            assert response.status_code == 200
            
            # Verify error cleared
            mock_complete_system['opcua'].write_multiple_variables.assert_called()
            
        finally:
            # Cleanup
            if file_id in cli_file_cache:
                del cli_file_cache[file_id]
```

---

## Stage 4 Testing: Frontend Integration

### Component Tests

#### Test File: `frontend/tests/components/MultiLayerJobControl.test.js`

```javascript
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import MultiLayerJobControl from '@/components/MultiLayerJobControl.vue'
import { usePrintJobStore } from '@/stores/printJobStore'

// Mock toast
vi.mock('vue-toastification', () => ({
  useToast: () => ({
    success: vi.fn(),
    error: vi.fn()
  })
}))

describe('MultiLayerJobControl', () => {
  let wrapper
  let store
  
  beforeEach(() => {
    setActivePinia(createPinia())
    store = usePrintJobStore()
    
    // Mock store methods
    store.startMultiLayerJob = vi.fn()
    store.cancelMultiLayerJob = vi.fn()
    store.clearSystemError = vi.fn()
    store.updateJobStatus = vi.fn()
  })

  const createWrapper = (props = {}) => {
    return mount(MultiLayerJobControl, {
      props: {
        selectedFile: {
          file_id: 'test-file-123',
          filename: 'test.cli',
          total_layers: 5
        },
        ...props
      },
      global: {
        stubs: {
          JobProgressDisplay: true,
          ErrorDisplayPanel: true
        }
      }
    })
  }

  it('renders job control interface', () => {
    wrapper = createWrapper()
    
    expect(wrapper.find('.multilayer-job-control').exists()).toBe(true)
    expect(wrapper.find('h3').text()).toBe('Multi-Layer Print Job')
    expect(wrapper.find('.start-job-btn').exists()).toBe(true)
  })

  it('displays file information correctly', () => {
    wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('test.cli')
    expect(wrapper.text()).toContain('Total Layers: 5')
  })

  it('enables start button when conditions are met', () => {
    wrapper = createWrapper()
    
    const startButton = wrapper.find('.start-job-btn')
    expect(startButton.attributes('disabled')).toBeUndefined()
  })

  it('disables start button when no file selected', () => {
    wrapper = createWrapper({ selectedFile: null })
    
    const startButton = wrapper.find('.start-job-btn')
    expect(startButton.attributes('disabled')).toBeDefined()
  })

  it('calls startMultiLayerJob when start button clicked', async () => {
    wrapper = createWrapper()
    
    store.startMultiLayerJob.mockResolvedValue({ success: true })
    
    await wrapper.find('.start-job-btn').trigger('click')
    
    expect(store.startMultiLayerJob).toHaveBeenCalledWith('test-file-123', 0)
  })

  it('shows active job section when job is active', async () => {
    store.multiLayerJob = {
      is_active: true,
      job_id: 'test-job-123',
      current_layer: 2,
      total_layers: 5,
      progress_percentage: 40,
      status: 'waiting_for_galvo'
    }
    
    wrapper = createWrapper()
    
    expect(wrapper.find('.active-job-section').exists()).toBe(true)
    expect(wrapper.find('.cancel-job-btn').exists()).toBe(true)
  })

  it('calls cancelJob when cancel button clicked', async () => {
    store.multiLayerJob = {
      is_active: true,
      job_id: 'test-job-123'
    }
    
    wrapper = createWrapper()
    
    store.cancelMultiLayerJob.mockResolvedValue({ success: true })
    
    await wrapper.find('.cancel-job-btn').trigger('click')
    
    expect(store.cancelMultiLayerJob).toHaveBeenCalled()
  })

  it('shows error section when system error occurs', async () => {
    store.multiLayerJob = {
      system_error: true,
      error_message: 'Test error message'
    }
    
    wrapper = createWrapper()
    
    expect(wrapper.find('.error-section').exists()).toBe(true)
  })
})
```

#### Test File: `frontend/tests/components/JobProgressDisplay.test.js`

```javascript
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import JobProgressDisplay from '@/components/JobProgressDisplay.vue'

describe('JobProgressDisplay', () => {
  const createWrapper = (jobStatus = {}) => {
    const defaultStatus = {
      job_id: 'test-job-123',
      current_layer: 3,
      total_layers: 10,
      progress_percentage: 30,
      start_time: Date.now() / 1000 - 300, // Started 5 minutes ago
      estimated_time_remaining: 700, // 11m 40s remaining
      waiting_for_print_start: false,
      waiting_for_layer_complete: true,
      waiting_for_galvo: false
    }
    
    return mount(JobProgressDisplay, {
      props: {
        jobStatus: { ...defaultStatus, ...jobStatus }
      }
    })
  }

  it('renders progress information correctly', () => {
    const wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('3 / 10')
    expect(wrapper.text()).toContain('30%')
    expect(wrapper.text()).toContain('test-job-123')
  })

  it('displays progress bar with correct width', () => {
    const wrapper = createWrapper()
    
    const progressFill = wrapper.find('.progress-fill')
    expect(progressFill.attributes('style')).toContain('width: 30%')
  })

  it('shows estimated time remaining', () => {
    const wrapper = createWrapper()
    
    expect(wrapper.text()).toContain('11m 40s')
  })

  it('highlights active status items', () => {
    const wrapper = createWrapper()
    
    const statusItems = wrapper.findAll('.status-item')
    
    // Check that the correct status item is active
    expect(statusItems[1].classes()).toContain('active') // Layer deposition should be active
    expect(statusItems[0].classes()).not.toContain('active') // Print start should not be active
    expect(statusItems[2].classes()).not.toContain('active') // Galvo should not be active
  })

  it('formats time correctly', () => {
    const wrapper = createWrapper({
      estimated_time_remaining: 3661 // 1h 1m 1s
    })
    
    expect(wrapper.text()).toContain('1h 1m 1s')
  })

  it('handles zero progress correctly', () => {
    const wrapper = createWrapper({
      current_layer: 1,
      progress_percentage: 0
    })
    
    expect(wrapper.text()).toContain('1 / 10')
    expect(wrapper.text()).toContain('0%')
  })
})
```

### Store Tests

#### Test File: `frontend/tests/stores/printJobStore.test.js`

```javascript
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { usePrintJobStore } from '@/stores/printJobStore'

// Mock API
const mockApi = {
  post: vi.fn(),
  get: vi.fn()
}

vi.mock('@/api', () => ({
  default: mockApi
}))

describe('printJobStore', () => {
  let store

  beforeEach(() => {
    setActivePinia(createPinia())
    store = usePrintJobStore()
    store.$api = mockApi
    vi.clearAllMocks()
  })

  describe('startMultiLayerJob', () => {
    it('successfully starts multi-layer job', async () => {
      const mockResponse = {
        data: {
          success: true,
          job_id: 'test-job-123',
          file_id: 'test-file-456',
          total_layers: 5
        }
      }
      
      mockApi.post.mockResolvedValue(mockResponse)
      mockApi.get.mockResolvedValue({
        data: {
          is_active: true,
          job_id: 'test-job-123',
          current_layer: 1,
          total_layers: 5
        }
      })

      const result = await store.startMultiLayerJob('test-file-456', 0)

      expect(mockApi.post).toHaveBeenCalledWith(
        '/print/cli/test-file-456/start-multilayer-job',
        { drum_id: 0 }
      )
      expect(result.success).toBe(true)
    })

    it('handles job start failure', async () => {
      mockApi.post.mockRejectedValue(new Error('Network error'))

      await expect(store.startMultiLayerJob('test-file-456')).rejects.toThrow('Network error')
    })
  })

  describe('updateJobStatus', () => {
    it('updates job status successfully', async () => {
      const mockStatus = {
        is_active: true,
        job_id: 'test-job-123',
        current_layer: 3,
        total_layers: 10,
        progress_percentage: 30,
        status: 'waiting_for_galvo'
      }

      mockApi.get.mockResolvedValue({ data: mockStatus })

      await store.updateJobStatus()

      expect(store.multiLayerJob).toEqual(mockStatus)
    })

    it('handles status update failure', async () => {
      mockApi.get.mockRejectedValue(new Error('API error'))

      await expect(store.updateJobStatus()).rejects.toThrow('API error')
    })
  })

  describe('cancelMultiLayerJob', () => {
    it('cancels job successfully', async () => {
      mockApi.post.mockResolvedValue({
        data: { success: true, message: 'Job cancelled' }
      })
      mockApi.get.mockResolvedValue({
        data: { is_active: false }
      })

      const result = await store.cancelMultiLayerJob()

      expect(mockApi.post).toHaveBeenCalledWith('/print/multilayer-job/cancel')
      expect(result.success).toBe(true)
    })
  })

  describe('clearSystemError', () => {
    it('clears system error successfully', async () => {
      mockApi.post.mockResolvedValue({
        data: { success: true }
      })
      mockApi.get.mockResolvedValue({
        data: { system_error: false, error_message: '' }
      })

      const result = await store.clearSystemError()

      expect(mockApi.post).toHaveBeenCalledWith('/print/multilayer-job/clear-error')
      expect(result.success).toBe(true)
    })
  })
})
```

---

## Performance Testing

### Load Testing

#### Test File: `backend/tests/performance/test_load.py`

```python
import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from fastapi.testclient import TestClient
from app.main import app

@pytest.mark.performance
class TestPerformanceLoad:
    """Performance and load testing"""
    
    def test_concurrent_status_requests(self):
        """Test handling of concurrent status requests"""
        client = TestClient(app)
        
        def make_status_request():
            return client.get("/print/multilayer-job/status")
        
        start_time = time.time()
        
        # Make 50 concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_status_request) for _ in range(50)]
            responses = [future.result() for future in futures]
        
        end_time = time.time()
        
        # All requests should succeed
        assert all(r.status_code == 200 for r in responses)
        
        # Should complete within reasonable time (adjust threshold as needed)
        assert end_time - start_time < 5.0
    
    @pytest.mark.asyncio
    async def test_opcua_communication_performance(self):
        """Test OPC UA communication performance"""
        from app.services.opcua_coordinator import opcua_coordinator
        
        if not opcua_coordinator.is_connected():
            await opcua_coordinator.connect()
        
        # Test batch write performance
        variables = {
            f"test_var_{i}": i for i in range(100)
        }
        
        start_time = time.time()
        
        # Note: This would need actual variables that exist in the PLC
        # await opcua_coordinator.write_multiple_variables(variables)
        
        end_time = time.time()
        
        # Should complete within reasonable time
        # assert end_time - start_time < 1.0
        
        await opcua_coordinator.disconnect()
    
    def test_memory_usage_during_long_job(self):
        """Test memory usage during extended job processing"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Simulate long-running job processing
        # This would involve creating and processing multiple jobs
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (adjust threshold as needed)
        max_increase = 50 * 1024 * 1024  # 50MB
        assert memory_increase < max_increase
```

## Test Execution Guidelines

### Running Tests by Stage

```bash
# Stage 1: OPC UA Infrastructure
pytest backend/tests/test_opcua_coordinator.py -v
pytest backend/tests/test_opcua_config.py -v
pytest backend/tests/integration/test_opcua_plc_integration.py -m integration

# Stage 2: Multi-Layer Job Management  
pytest backend/tests/test_multilayer_job.py -v
pytest backend/tests/test_multilayer_api.py -v

# Stage 3: Coordination Logic
pytest backend/tests/integration/test_coordination_engine.py -v
pytest backend/tests/e2e/test_complete_workflow.py -m e2e

# Stage 4: Frontend Integration
npm test -- tests/components/
npm test -- tests/stores/

# Performance Tests
pytest backend/tests/performance/ -m performance

# All Tests
pytest backend/tests/ -v
npm test
```

### Test Environment Setup

#### Docker Compose for Testing (`docker-compose.test.yml`)

```yaml
version: '3.8'
services:
  opcua-server:
    image: opcua/opcua-server:latest
    ports:
      - "4840:4840"
    environment:
      - OPCUA_SERVER_PORT=4840
      - OPCUA_SECURITY_POLICY=None
    volumes:
      - ./test-config/opcua-config.xml:/config/server.xml

  mock-aerosint:
    build:
      context: ./tests/mocks/aerosint-server
    ports:
      - "8080:8080"
    environment:
      - MOCK_MODE=true

  test-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.test
    depends_on:
      - opcua-server
      - mock-aerosint
    environment:
      - OPCUA_ENDPOINT=opc.tcp://opcua-server:4840
      - AEROSINT_BASE_URL=http://mock-aerosint:8080
    volumes:
      - ./backend:/app
    command: pytest -v

  test-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.test
    volumes:
      - ./frontend:/app
    command: npm test
```

### Continuous Integration

#### GitHub Actions Workflow (`.github/workflows/test.yml`)

```yaml
name: Multi-Layer Implementation Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      opcua-server:
        image: opcua/opcua-server:latest
        ports:
          - 4840:4840
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-mock
    
    - name: Run unit tests
      run: |
        cd backend
        pytest tests/ -v --ignore=tests/integration --ignore=tests/e2e
    
    - name: Run integration tests
      run: |
        cd backend
        pytest tests/integration/ -v -m integration
    
    - name: Run E2E tests
      run: |
        cd backend
        pytest tests/e2e/ -v -m e2e

  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run tests
      run: |
        cd frontend
        npm test
    
    - name: Run E2E tests
      run: |
        cd frontend
        npm run test:e2e
```

This comprehensive testing guide ensures that each stage of the multi-layer implementation is thoroughly validated before proceeding to the next stage, reducing the risk of integration issues and ensuring system reliability.
