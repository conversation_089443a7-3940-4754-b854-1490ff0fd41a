# Recoater HMI System Architecture

## Overview

The Recoater HMI system is a modern web-based interface that communicates with the recoater hardware through a FastAPI backend. The system is designed with a clear separation of concerns between the frontend and backend components.

## System Components

```mermaid
graph TD
    A[User] -->|Interacts with| B[Web Browser]
    B -->|REST API / WebSocket| C[FastAPI Backend]
    C -->|HTTP REST API| D[Recoater Hardware Server]
    D -->|Hardware Bus| E[Recoater Hardware]
    C -->|Mock Mode| F[Mock Recoater Client]
    
    subgraph "Recoater Hardware Server"
        D -->|Ethernet| G[HTTP Server]
        G --> H[Command Processor]
        H --> I[Hardware Interface]
    end
```

### Recoater Hardware Server

The Recoater Hardware Server is the bridge between our FastAPI backend and the physical recoater hardware. It runs directly on the recoater's control system and provides a REST API for remote control and monitoring.

**Key Components:**
- **HTTP Server**: Lightweight web server (typically running on port 8080)
- **Command Processor**: Validates and processes incoming commands
- **Hardware Interface**: Low-level communication with hardware controllers

**Server Details:**
- **Base URL**: `http://172.16.17.224:8080` (configurable)
- **Protocol**: HTTP/1.1
- **Authentication**: Basic Auth (if configured)
- **Timeout**: 5 seconds (configurable)

**Example API Endpoints:**
- `GET /axis/x` - Get X-axis status
- `POST /axis/x/move` - Move X-axis
- `GET /drum/1/suction` - Get drum suction status
- `PUT /drum/1/ejection` - Set drum ejection pressure

### 1. Frontend (Vue.js)
- **Technology Stack**:
  - Vue.js 3 with Composition API
  - Pinia for state management
  - Axios for HTTP requests
  - WebSocket for real-time updates

- **Key Features**:
  - Real-time status monitoring
  - Axis control interface
  - Configuration management
  - Error handling and notifications

### 2. Backend (FastAPI)
- **Core Components**:
  - **API Endpoints**: RESTful endpoints for system control
  - **WebSocket Server**: Real-time bidirectional communication
  - **Connection Manager**: Handles WebSocket connections
  - **Recoater Client**: Interface to the physical hardware
  - **Mock Client**: For development and testing without hardware

- **Key Features**:
  - Authentication and authorization
  - Request validation
  - Error handling
  - Logging and monitoring
  - CORS support

### 3. Communication Flow

1. **Initial Connection**:
   - Frontend establishes WebSocket connection to backend
   - Backend authenticates and authorizes the connection

2. **Status Updates**:
   - Backend polls hardware status at regular intervals
   - Status updates are broadcast to all connected clients
   - Frontend updates UI in real-time

3. **Control Commands**:
   - User actions in the UI trigger API calls to the backend
   - Backend validates and forwards commands to hardware
   - Command status is reported back via WebSocket

### 4. Hardware Communication

#### Command Flow

```mermaid
sequenceDiagram
    participant F as Frontend
    participant B as Backend
    participant S as Hardware Server
    participant H as Hardware Controller
    
    F->>B: HTTP POST /api/v1/axis/x/move {distance: 100, speed: 10}
    B->>S: HTTP POST /axis/x/move {distance: 100, speed: 10}
    S->>H: Send motor command
    H-->>S: Command acknowledgment
    S-->>B: HTTP 200 OK
    B-->>F: HTTP 200 OK {success: true}
    
    loop Status Polling (1s interval)
        B->>S: HTTP GET /axis/x/status
        S->>H: Read position/sensors
        H-->>S: Return status
        S-->>B: Return status data
        B->>F: WebSocket update
    end
```

#### Hardware Server API

The hardware server implements the following main endpoints:

##### Axis Control
- `GET /axis/{axis}` - Get axis status (position, moving state, etc.)
- `POST /axis/{axis}/move` - Move axis (relative/absolute)
- `POST /axis/{axis}/home` - Home axis
- `DELETE /axis/{axis}/motion` - Stop axis motion

##### Drum Control
- `GET /drum/{id}/suction` - Get suction status
- `PUT /drum/{id}/suction` - Set suction pressure
- `GET /drum/{id}/ejection` - Get ejection status
- `PUT /drum/{id}/ejection` - Set ejection pressure
- `GET /drum/{id}/motion` - Get motion status
- `POST /drum/{id}/motion` - Start drum motion
- `DELETE /drum/{id}/motion` - Stop drum motion

##### System
- `GET /status` - Get system status
- `GET /health` - Health check endpoint

#### Error Handling

1. **Connection Errors**:
   - Server unreachable
   - Timeout (5s default)
   - Connection refused

2. **API Errors**:
   - 400: Invalid request
   - 401: Unauthorized
   - 403: Forbidden
   - 404: Endpoint not found
   - 500: Internal server error

3. **Hardware Errors**:
   - Axis limit reached
   - Motor fault
   - Emergency stop
   - Communication timeout

### 5. Development vs Production Modes

- **Development Mode**:
  - Uses MockRecoaterClient for testing without hardware
  - Simulates hardware responses
  - Enables rapid development and testing

- **Production Mode**:
  - Connects to actual recoater hardware
  - Implements proper error handling and timeouts
  - Includes additional safety checks

## Security Considerations

### Authentication
- **Hardware Server**: Basic Auth (if enabled)
- **Backend API**: JWT token-based authentication
- **WebSocket**: Authenticated during initial handshake

### Network Security
- **Firewall Rules**: Only allow connections from trusted networks
- **TLS/SSL**: All communications should be encrypted in production
- **CORS**: Strict origin checking for web interface
- **Rate Limiting**: Prevent abuse of API endpoints

### Operational Security
- **Command Validation**: All inputs are validated before processing
- **Error Handling**: No sensitive information in error messages
- **Logging**: All operations are logged for audit purposes
- **Timeouts**: Configurable timeouts for all operations

## Error Handling

- Backend provides detailed error messages
- Frontend displays user-friendly error messages
- System automatically recovers from connection losses
- Critical errors are logged for debugging

## Performance Considerations

- WebSocket keeps connection alive for real-time updates
- Backend implements connection pooling for hardware communication
- Frontend optimizes re-renders for smooth UI updates
- Large data transfers are paginated where applicable
