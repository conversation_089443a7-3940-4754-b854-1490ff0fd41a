/**
 * Test suite for Legend component.
 * 
 * This module tests the legend component to ensure it renders correctly
 * with proper colors, labels, and styling for the three drum indicators.
 */

import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import Legend from '../Legend.vue'

describe('Legend', () => {
  let wrapper

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('renders correctly with all three drum items', () => {
    wrapper = mount(Legend)

    // Check that the legend container exists
    expect(wrapper.find('.legend').exists()).toBe(true)

    // Check that all three legend items exist
    const legendItems = wrapper.findAll('.legend-item')
    expect(legendItems).toHaveLength(3)

    // Check that all three swatches exist
    const swatches = wrapper.findAll('.legend-swatch')
    expect(swatches).toHaveLength(3)

    // Check that all three labels exist
    const labels = wrapper.findAll('.legend-label')
    expect(labels).toHaveLength(3)
  })

  it('displays correct labels for all drums', () => {
    wrapper = mount(Legend)

    const labels = wrapper.findAll('.legend-label')
    expect(labels[0].text()).toBe('Drum 0')
    expect(labels[1].text()).toBe('Drum 1')
    expect(labels[2].text()).toBe('Drum 2')
  })

  it('applies correct background colors to swatches', () => {
    wrapper = mount(Legend)

    const drum0Swatch = wrapper.find('.drum-0')
    const drum1Swatch = wrapper.find('.drum-1')
    const drum2Swatch = wrapper.find('.drum-2')

    expect(drum0Swatch.exists()).toBe(true)
    expect(drum1Swatch.exists()).toBe(true)
    expect(drum2Swatch.exists()).toBe(true)

    // Check computed styles for background colors
    const drum0Style = getComputedStyle(drum0Swatch.element)
    const drum1Style = getComputedStyle(drum1Swatch.element)
    const drum2Style = getComputedStyle(drum2Swatch.element)

    // Convert hex colors to rgb for comparison (browsers return rgb format)
    expect(drum0Style.backgroundColor).toBe('rgb(52, 152, 219)') // #3498db (blue)
    expect(drum1Style.backgroundColor).toBe('rgb(230, 126, 34)')  // #e67e22 (orange)
    expect(drum2Style.backgroundColor).toBe('rgb(39, 174, 96)')  // #27ae60 (green)
  })

  it('has proper container styling and padding', () => {
    wrapper = mount(Legend)

    const legendContainer = wrapper.find('.legend')
    const containerStyle = getComputedStyle(legendContainer.element)

    // Check padding
    expect(containerStyle.padding).toBe('16px')
    
    // Check display and alignment
    expect(containerStyle.display).toBe('flex')
    expect(containerStyle.alignItems).toBe('center')
    
    // Check gap between items
    expect(containerStyle.gap).toBe('8px')
    
    // Check margin
    expect(containerStyle.margin).toBe('12px 0px')
  })

  it('has correct swatch dimensions and styling', () => {
    wrapper = mount(Legend)

    const swatches = wrapper.findAll('.legend-swatch')
    
    swatches.forEach(swatch => {
      const swatchStyle = getComputedStyle(swatch.element)
      
      // Check dimensions
      expect(swatchStyle.width).toBe('24px')
      expect(swatchStyle.height).toBe('24px')
      
      // Check border radius
      expect(swatchStyle.borderRadius).toBe('2px')
      
      // Check margin
      expect(swatchStyle.marginRight).toBe('8px')
    })
  })

  it('has correct label styling', () => {
    wrapper = mount(Legend)

    const labels = wrapper.findAll('.legend-label')
    
    labels.forEach(label => {
      const labelStyle = getComputedStyle(label.element)
      
      // Check font properties
      expect(labelStyle.fontSize).toBe('14px')
      expect(labelStyle.fontWeight).toBe('400')
      expect(labelStyle.color).toBe('rgb(44, 62, 80)') // #2c3e50 in rgb
    })
  })

  it('has proper legend item layout', () => {
    wrapper = mount(Legend)

    const legendItems = wrapper.findAll('.legend-item')
    
    legendItems.forEach(item => {
      const itemStyle = getComputedStyle(item.element)
      
      // Check that each item is a flex container with proper alignment
      expect(itemStyle.display).toBe('flex')
      expect(itemStyle.alignItems).toBe('center')
    })
  })

  it('renders swatches with correct CSS classes', () => {
    wrapper = mount(Legend)

    // Check that each swatch has the correct drum-specific class
    expect(wrapper.find('.legend-swatch.drum-0').exists()).toBe(true)
    expect(wrapper.find('.legend-swatch.drum-1').exists()).toBe(true)
    expect(wrapper.find('.legend-swatch.drum-2').exists()).toBe(true)
  })

  it('maintains proper structure and hierarchy', () => {
    wrapper = mount(Legend)

    // Check the overall structure
    const legend = wrapper.find('.legend')
    expect(legend.exists()).toBe(true)

    // Check that each legend item contains a swatch and label
    const legendItems = wrapper.findAll('.legend-item')
    
    legendItems.forEach((item, index) => {
      const swatch = item.find('.legend-swatch')
      const label = item.find('.legend-label')
      
      expect(swatch.exists()).toBe(true)
      expect(label.exists()).toBe(true)
      
      // Verify the swatch has the correct drum class
      expect(swatch.classes()).toContain(`drum-${index}`)
      
      // Verify the label has the correct text
      expect(label.text()).toBe(`Drum ${index}`)
    })
  })
})