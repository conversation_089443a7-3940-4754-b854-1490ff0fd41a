"""
Application Dependencies
========================

This module holds shared application dependencies to avoid circular imports.
It provides a centralized location for dependency injection patterns.
"""

import os
import logging
from typing import Optional
from fastapi import HTTPException

from services.recoater_client import Recoater<PERSON>lient
from services.mock_recoater_client import MockRecoaterClient

logger = logging.getLogger(__name__)

# Global recoater client instance
_recoater_client: Optional[RecoaterClient] = None


def initialize_recoater_client() -> None:
    """
    Initialize the global recoater client instance.
    
    This should be called during application startup.
    """
    global _recoater_client
    
    base_url = os.getenv("RECOATER_API_BASE_URL", "http://172.16.17.224:8080")
    development_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"

    if development_mode:
        logger.info("Initializing mock recoater client for development mode")
        _recoater_client = MockRecoaterClient(base_url)
    else:
        logger.info("Initializing real recoater client for production mode")
        _recoater_client = RecoaterClient(base_url)


def get_recoater_client() -> RecoaterClient:
    """
    Dependency function to get the recoater client instance.
    
    This function can be used with FastAPI's Depends() to inject
    the recoater client into API endpoints.
    
    Returns:
        RecoaterClient: The initialized recoater client instance
        
    Raises:
        HTTPException: If the recoater client is not initialized
    """
    if _recoater_client is None:
        logger.error("Recoater client not initialized")
        raise HTTPException(
            status_code=503, 
            detail="Recoater client not initialized"
        )
    return _recoater_client


def get_recoater_client_instance() -> Optional[RecoaterClient]:
    """
    Get the recoater client instance without raising exceptions.
    
    This is useful for internal application code that needs to access
    the client but doesn't want to handle HTTP exceptions.
    
    Returns:
        Optional[RecoaterClient]: The recoater client instance or None if not initialized
    """
    return _recoater_client
