/**
 * Recoater HMI Frontend - Main Application Entry Point
 * ===================================================
 * 
 * This is the main entry point for the Vue.js frontend application.
 * It sets up the Vue app, router, state management, and global configurations.
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import './style.css'

// Create Vue application
const app = createApp(App)

// Add Pinia for state management
app.use(createPinia())

// Add Vue Router
app.use(router)

// Mount the application
app.mount('#app')
