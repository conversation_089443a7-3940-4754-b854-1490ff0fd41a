# Recoater HMI - Operator's User Guide

**Version 1.3**

## 1. Introduction

Welcome to the Recoater HMI (Human-Machine Interface). This guide provides comprehensive instructions for new operators to effectively control and monitor the Aerosint SPD Recoater using this web-based application.

The HMI is designed to be an intuitive, reliable interface that replaces the previous developer-focused tools. It allows you to manage all aspects of the recoater's operation, from individual component control to executing a full print job.

This guide is structured to follow the layout of the application, with sections corresponding to each of the main views available in the navigation sidebar.

---

## 2. The Main Interface

![Main Interface](images/main-interface.png)

The HMI is composed of three primary areas:

1.  **Header:** Located at the top, it displays the application title and the all-important **Connection Status Indicator**.
2.  **Navigation Sidebar:** On the left, this bar allows you to switch between the HMI's main functional views.
3.  **Content Area:** The central part of the screen where the controls and information for the selected view are displayed.

### 2.1. The Connection Status Indicator

This indicator, located in the top-right corner, is the most critical element for understanding the system's state. **Always check its status before attempting any operation.**

![Connection Status Indicator](images/connection-status.png)

*   <span style="color: #27ae60;">**● Connected**</span>: The HMI is successfully communicating with the recoater hardware. The system is operational.
*   <span style="color: #f39c12;">**● Error**</span>: The HMI is connected, but the recoater hardware is reporting an error. You may need to check the hardware for issues.
*   <span style="color: #e74c3c;">**● Disconnected**</span>: The HMI cannot communicate with the recoater backend. This could be due to a network issue, or the backend service or recoater hardware not being turned on.

---

## 3. Status View (Home Screen)

This is the default view when you first load the application. It provides a high-level summary of the system's connection status.

![Status View](images/status-view.png)

*   **Backend Status:** Shows if your browser is connected to the HMI's backend software.
*   **Recoater Status:** Shows if the HMI's backend is successfully connected to the physical recoater hardware.
*   **Last Update:** Timestamps the last successful communication.
*   **Error Information:** If an error is present, this card will appear with a description of the problem.

The **Refresh Status** button allows you to manually request an immediate update from the system.


<!-- ## 4. Axis Control View


<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 1rem; margin-bottom: 1.5rem;">
    <h3 style="margin: 0 0 0.5rem 0; color: #856404;">⚠️ Hardware Limitation Notice</h3>
    <p style="margin: 0; color: #856404;">
        As per the hardware's API specification (<code>openapi.json</code>), endpoints for direct axis and gripper control are not defined. Therefore, all controls on this screen are **disabled** for production use. This view is intended for development and testing with mock data and should not be used for machine operation.
    </p>
</div> -->

---

## 4. Recoater Dashboard

This is the primary dashboard for controlling the individual components of the recoater head. It is divided into sections for Drums, Hoppers (scraping blades), and the Leveler.

### 4.1. Drum Controls

This section contains a control card for each of the three drums (Drum 0, Drum 1, Drum 2). Each card allows you to monitor and control its associated drum.

![Drum Status](images/drum-status.png)

**Status Display:**
*   **Position:** The current rotational position of the drum in millimeters (mm).
*   **Circumference:** The total circumference of the drum in millimeters (mm).
*   **Running/Stopped:** A status light indicates if the drum is currently in motion.

**Motion Control:**
You can command the drum to move using several modes. The `Start Motion` button is disabled if the drum is already running.

![Drum Motion](images/drum-motion.png)

*   **Mode:**
    *   `Relative`: Moves the drum by the specified `Distance (mm)`.
    *   `Absolute`: Moves the drum to a specific absolute `Position (mm)`.
    *   `Turns`: Rotates the drum by the specified number of `Turns`.
    *   `Speed`: Rotates the drum continuously at the specified `Speed (mm/s)`.
    *   `Homing`: Returns the drum to its zero-reference position.
*   **Speed (mm/s):** The speed of the movement.
*   **Buttons:**
    *   `Start Motion`: Initiates the configured movement.
    *   `Cancel Motion`: Immediately stops any ongoing drum movement. This is only enabled when the drum is running.

**Pressure Controls:**

![Drum Pressure](images/drum-pressure.png)

*   **Ejection Pressure:**
    *   Displays the current and target pressure.
    *   Set a new `Target` pressure and select the `Unit` (`Pa` for Pascals, `bar` for Bar). Click `Set` to apply.
*   **Suction Pressure:**
    *   Displays the current and target pressure in Pascals (Pa).
    *   Set a new `Target` pressure. Click `Set` to apply.

### 4.2. Hopper Controls (Scraping Blades)

This section contains a control card for each drum's hopper and scraping blade assembly.

![Hopper Status](images/hopper-status.png)

**Status Display:**
*   **Screw Positions:** Shows the current position of each of the two blade screws in micrometers (µm).
*   **Running/Stopped:** A status light indicates if any part of the blade assembly is in motion.


![Hopper Motion](images/hopper-motion.png)

**Collective Blade Motion:**
Controls both blade screws together.
*   **Mode:**
    *   `Relative`: Moves both screws by the specified `Distance (µm)`.
    *   `Absolute`: Moves both screws to a specific `Position (µm)`.
    *   `Homing`: Returns both screws to their zero-reference position. The distance input is hidden in this mode.
*   **Buttons:** `Start Motion` and `Cancel Motion` function similarly to the drum controls.

![Hopper Screw](images/hopper-screw.png)

**Individual Screw Control:**
Allows for fine-tuning of each screw.
*   Enter a relative `Distance (µm)` for the specific screw.
*   Click `Move` to start the motion for that screw only.
*   Click `Stop` to cancel motion for that screw only.

### 4.3. Leveler Control

This card manages the powder leveler.

![Leveler](images/leveler.png)

*   **Pressure Control:** Displays the `Current`, `Target`, and `Maximum` allowed pressure in Pascals (Pa). You can input a new target and click `Set` to apply it.
*   **Magnetic Sensor:** Displays the state of the leveler's magnetic sensor as either `Active (Field Detected)` or `Inactive`.

---

## 5. Print Control View

This view consolidates all functions related to preparing and executing a print job.

### 5.1. Layer Parameters

These parameters define how the recoater will process the current layer.

![Layer Parameters](images/layer-parameters.png)

*   **Filling Drum ID:** The ID of the drum (0, 1, or 2) containing the filling material. Set to **-1** for no filling.
*   **Patterning Speed (mm/s):** The speed at which the recoater will operate.
*   **X Offset (mm):** An offset to apply along the X-axis.
*   **Enable Powder Saving:** Toggles the use of powder-saving strategies.

Use the `Load Current` button to fetch the existing parameters from the hardware, and `Save Parameters` to apply your changes.

### 5.2. Layer Preview

This area shows a PNG image of what the layer will look like, with color-coded geometry to distinguish between different drums.

![Layer Preview](images/layer-preview.png)

**Color Legend:**
A color legend is displayed above the preview showing the color mapping for each drum:
*   **Blue (#3498db)**: Drum 0 geometry
*   **Orange (#e67e22)**: Drum 1 geometry
*   **Green (#27ae60)**: Drum 2 geometry

*   **Preview Source (Dropdown):**
    *   `Current Layer Configuration`: Generates a color-coded preview showing how geometry would be distributed across drums. Each drum's geometry is rendered in its designated color.
    *   `Drum [0-2] Geometry`: Loads and displays the preview of the geometry file *currently stored* on the selected drum, rendered in that drum's specific color.
*   **Load Preview (Button):** Generates and displays the image based on the selected source.

### 5.3. File Management (PNG/CLI Geometries)

This section allows you to manage the geometry files stored on the recoater drums.

![File Management](images/file-management.png)

*   **Upload:**
    1.  Select a `Target Drum` from the dropdown.
    2.  Click the file input to choose a PNG or CLI file from your computer.
    3.  Click `Upload File`.

    **Note:** In 5.3 the HMI sends your file's raw bytes directly to the drum hardware. No parsing or conversion is performed—the file must be in a format compatible with the drum firmware (typically ASCII CLI or PNG).

*   **Download / Delete:**
    1.  Select a drum from the `Select Drum` dropdown in the "Manage Existing Files" section.
    2.  Click `Download Geometry` to save the stored file to your computer.
    3.  Click `Delete Geometry` to remove the file from the selected drum (a confirmation prompt will appear).

### 5.4. Multi-Layer CLI File Workflow

This is an advanced feature for working with standard multi-layer CLI files (both ASCII and binary formats). It allows you to visualize individual layers and send specific layer geometries to drums for printing.

**Supported CLI Formats:**
*   **Binary CLI**: Compressed binary format (automatically detected by header)
*   **ASCII CLI**: Text-based format with space-delimited or slash-delimited commands

![CLI Upload](images/cli-upload.png)

*   **Step 1: Upload & Parse**
    1.  In the **CLI Layer Preview** section, use the file input to select a multi-layer CLI file.
    2.  Click **Upload & Parse CLI**.
    3.  The HMI automatically detects the file format and parses it in memory, displaying the **File ID** and **Total Layers** found.
    4.  **Note:** This parsing step happens entirely on the HMI server—nothing is sent to the drum until Step 3.
    5.  **Note:** The system has a 5-minute timeout for CLI parsing. If the parsing takes longer than 5 minutes, the operation will be canceled. Split large files if needed.

![CLI Layer Preview](images/preview-cli-layer.png)

*   **Step 2: Preview Individual Layers**
    1.  Once parsed, use the **Layer Number** input and click **Preview Layer**.
    2.  Only the selected layer is rendered and displayed as a PNG preview.
    3.  **Note:** CLI layer previews use a different color scheme than the main layer preview. Individual CLI layers show polylines in black and hatch patterns in blue, as they represent raw geometry data rather than drum-specific assignments.

![CLI Send to Drum](images/send-cli-layer.png)

*   **Step 3: Send a Single Layer to the Recoater**
    1.  Select **Layer Number** and **Target Drum**.
    2.  Click **Send Layer to Recoater**.
    3.  The HMI extracts the 2D geometry data (polylines and hatches) from the selected layer and generates a new **ASCII CLI file** containing only that layer's geometry (regardless of the original file format). This ensures compatibility with the drum firmware which requires ASCII CLI format. The ASCII CLI file is then uploaded to the drum (overwriting any existing file on that drum).
    4.  **Note:** The layer geometry is converted to ASCII CLI format (compatible with the hardware), not sent as a PNG image. The PNG preview is only for visualization purposes. The full parsed CLI file remains in HMI memory (keyed by File ID) until cleared or the server restarts, and can be re‐used for further previews or sends without re-uploading.

### 5.5. CLI Layer Range Selection (Batch Processing)

This advanced feature allows you to send multiple consecutive layers from a parsed CLI file to a drum in a single operation, enabling efficient batch processing for multi-layer manufacturing workflows.

*   **Prerequisites:** You must first upload and parse a multi-layer CLI file using the steps in section 5.4.

*   **How Layer Range Processing Works:**
    *   The HMI extracts the specified layer range from the parsed CLI file
    *   Multiple layers' geometry data (polylines and hatches) are combined into a single optimized **ASCII CLI file**
    *   This combined ASCII CLI file contains all the layers in the range with proper layer commands and Z-height information
    *   The resulting file is sent to the selected drum, replacing any existing geometry
    *   Each drum can only store one geometry file at a time

**Generated CLI Format:**
The system generates ASCII CLI files with the standard structure including header, \$\$LAYER commands, \$\$POLYLINE commands, and \$\$HATCHES commands. This ensures compatibility with drum firmware that requires ASCII CLI formatting.

*   **Default Values:** When a CLI file is uploaded, the End Layer Number automatically defaults to the total number of layers in the file for convenience.

![CLI Layer Range](images/send-cli-range.png)

*   **Layer Range Selection:**
    1.  In the **CLI Layer Selection** section, specify the **Start Layer Number** and **End Layer Number** (both 1-indexed).
    2.  Select the **Target Drum** (0, 1, or 2) where the layer range should be sent.
    3.  Ensure the range is valid: start layer ≤ end layer, and both within the total number of layers.
    4.  The interface validates your range and displays the number of layers to be processed.

*   **Batch Send Operation:**
    1.  Click **Send Layer Range** to initiate the batch operation.
    2.  The system processes layers sequentially and provides progress feedback.
    3.  Upon completion, you'll receive confirmation that the range was sent successfully.
    4.  **Note:** The original parsed CLI file remains available in memory for additional operations.

*   **Important Considerations:**
    *   **Processing time**: Layer range operations may take time for large ranges
    *   **Storage limitation**: Each drum overwrites its previous content when receiving a new layer range
    *   **Memory efficiency**: The original CLI file structure and metadata are preserved for reuse
    *   **Validation**: The system validates layer numbers and drum availability before processing

### 5.6. Print Job Management

This section controls the execution of the print process.

![Print Job Management](images/print-job-management.png)

*   **Status Display:**
    *   `Current State`: Shows if the system is `Ready`, `Printing`, or in an `Error` state.
    *   `Print Active`: A clear Yes/No indicator.
    *   `Has Errors`: A clear Yes/No indicator.
*   **Controls:**
    *   `Start Print Job`: Begins the printing process. This is only enabled when the system is `Ready` and not already printing.
    *   `Cancel Print Job`: Stops an active print job. This is only enabled during printing and will require confirmation.
    *   `Refresh Status`: Manually queries the hardware for the latest job status.

### 5.7. Troubleshooting

**CLI File Issues:**
*   **"Unrecognized directive" warnings**: Some CLI files may contain non-standard directives that are safely ignored during parsing.
*   **Upload failures**: Check that the CLI file is not corrupted and is in ASCII or binary CLI format.
*   **Layer range errors**: Ensure start layer ≤ end layer and both are within the total layer count.

**Preview Issues:**
*   **Color-coded previews**: The main layer preview uses drum-specific colors (blue, orange, green) while CLI layer previews use black/blue coloring. This is normal and expected behavior.
*   **Preview generation failures**: If color-coded previews fail to generate, the system will fall back to simple placeholder images.

**Hardware Communication:**
*   **Connection issues**: Check the connection status indicator in the header before attempting operations.
*   **Upload timeouts**: Large layer ranges may take time to process - wait for completion confirmation.

---

## 6. Configuration View

This view is for setting persistent, system-wide hardware parameters. **Changes made here affect the machine's core configuration and should be done with care.**

![Configuration View](images/configuration-view.png)

*   **Build Space Configuration:** Set the machine's physical build volume dimensions (diameter, length, width).
*   **System Configuration:** Set the `Resolution (µm)` and `Ejection Matrix Size`.
*   **Drum Gap Configuration:**
    *   Displays a list of the gaps between the drums in millimeters (mm).
    *   Use the `Add Gap` and `Remove Gap` buttons to modify the list to match your hardware setup.
*   **Action Buttons:**
    *   `Save Configuration`: Applies all changes and saves them to the recoater hardware.

---

## 7. Technical Specifications

**Hardware Limitations:**
*   **Drum Count**: System supports exactly 3 drums (IDs 0, 1, 2)
*   **CLI Format**: Hardware requires ASCII CLI format for geometry data
*   **File Storage**: Each drum can store only one geometry file at a time

**Supported File Formats:**
*   **PNG**: Direct upload to drums (section 5.3)
*   **ASCII CLI**: Direct upload or generated from parsed multi-layer files
*   **Binary CLI**: Parsed and converted to ASCII CLI for hardware compatibility

**Preview System:**
*   **Color-Coded Rendering**: Layer previews use drum-specific colors for visual distinction
*   **Drum Color Mapping**: Drum 0 (Blue #3498db), Drum 1 (Orange #e67e22), Drum 2 (Green #27ae60)
*   **Fallback Support**: System includes fallback mechanisms for preview generation failures

**System Requirements:**
*   **Network Connection**: Required for communication between HMI and recoater hardware
*   **Browser Compatibility**: Modern web browsers with WebSocket support
*   **File Size Limits**: CLI files should be reasonable size for processing and transmission