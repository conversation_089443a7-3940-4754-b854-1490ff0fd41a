# Recoater Custom HMI

This project provides a modern, intuitive, and reliable web-based Human-Machine Interface (HMI) for the Aerosint SPD Recoater system, designed to replace the default SwaggerUI.

The application is built with a robust backend/frontend architecture to ensure high uptime and a responsive user experience, making it suitable for operators in an industrial or laboratory setting.

## Features

-   **Intuitive UI:** A clean interface designed based on the Aerosint User Manual for easy operation.
-   **Real-time Monitoring:** Live status updates for system state, axis positions, and process parameters via WebSockets.
-   **Full Functionality:** Implements all major recoater controls including axis motion, drum/hopper/leveler management, and print job execution.
-   **Component-Based:** Built with modern web technologies for maintainability and future expansion.

## Technology Stack

-   **Backend:** Python 3.9+ with **FastAPI**
-   **Frontend:** **Vue.js 3** with **Vite**
-   **Real-time Communication:** WebSockets

## Getting Started

### Prerequisites

-   Node.js v16+
-   Python v3.9+
-   A running and accessible Aerosint Recoater system.

### Installation & Running

1.  **Configure Backend:**
    ```bash
    cd backend
    python -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate
    pip install -r requirements.txt
    cp .env.example .env
    # Edit .env and set the RECOATER_API_ADDRESS
    ```

2.  **Configure Frontend:**
    ```bash
    cd frontend
    npm install
    ```

3.  **Run the Application:**
    -   In one terminal, run the backend:
        ```bash
        cd backend
        uvicorn app.main:app --reload
        ```
    -   In another terminal, run the frontend:
        ```bash
        cd frontend
        npm run dev
        ```

4.  **Access the HMI:**
    Open your web browser (Firefox, Chrome, or Edge) and navigate to the address provided by the Vite development server (usually `http://localhost:5173`).