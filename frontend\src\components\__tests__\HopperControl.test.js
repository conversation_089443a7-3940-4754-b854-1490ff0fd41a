import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import HopperControl from '../HopperControl.vue'
import apiService from '@/services/api'

// Mock the API service
vi.mock('@/services/api', () => ({
  default: {
    setBladeScrewsMotion: vi.fn(),
    cancelBladeScrewsMotion: vi.fn(),
    setBladeScrewMotion: vi.fn(),
    cancelBladeScrewMotion: vi.fn()
  }
}))

describe('HopperControl', () => {
  let wrapper

  const defaultProps = {
    drumId: 0,
    bladeScrews: [
      { id: 0, position: 1000.0, running: false },
      { id: 1, position: 2000.0, running: false }
    ],
    bladeMotion: {},
    connected: true
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  const createWrapper = (props = {}) => {
    return mount(HopperControl, {
      props: {
        ...defaultProps,
        ...props
      }
    })
  }

  describe('Component Rendering', () => {
    it('renders the hopper control card with correct title', () => {
      wrapper = createWrapper()

      expect(wrapper.find('h3').text()).toBe('Hopper 0')
      expect(wrapper.text()).toContain('Collective Blade Motion')
      expect(wrapper.text()).toContain('Individual Screw Control')
    })

    it('displays blade screws status when provided', () => {
      wrapper = createWrapper()

      expect(wrapper.text()).toContain('Screw 0')
      expect(wrapper.text()).toContain('Screw 1')
      expect(wrapper.text()).toContain('1000.0 µm')
      expect(wrapper.text()).toContain('2000.0 µm')
    })

    it('shows stopped status when no screws are running', () => {
      wrapper = createWrapper()
      
      expect(wrapper.text()).toContain('Stopped')
    })

    it('shows running status when any screw is running', () => {
      wrapper = createWrapper({
        bladeScrews: [
          { id: 0, position: 1000.0, running: true },
          { id: 1, position: 2000.0, running: false }
        ]
      })
      
      expect(wrapper.text()).toContain('Running')
    })

    it('disables controls when not connected', () => {
      wrapper = createWrapper({ connected: false })
      
      const buttons = wrapper.findAll('button')
      const inputs = wrapper.findAll('input')
      const selects = wrapper.findAll('select')
      
      buttons.forEach(button => {
        expect(button.attributes('disabled')).toBeDefined()
      })
      
      inputs.forEach(input => {
        expect(input.attributes('disabled')).toBeDefined()
      })
      
      selects.forEach(select => {
        expect(select.attributes('disabled')).toBeDefined()
      })
    })
  })

  describe('Collective Motion Controls', () => {
    it('has default motion parameters', () => {
      wrapper = createWrapper()
      
      const modeSelect = wrapper.find('select')
      const distanceInput = wrapper.find('input[type="number"]')
      
      expect(modeSelect.element.value).toBe('relative')
      expect(distanceInput.element.value).toBe('1000')
    })

    it('hides distance input for homing mode', async () => {
      wrapper = createWrapper()

      const modeSelect = wrapper.find('select')
      await modeSelect.setValue('homing')

      // Look for the collective motion distance input by checking if the form group is hidden
      const distanceFormGroup = wrapper.find('.parameter-grid .form-group:nth-child(2)')
      expect(distanceFormGroup.exists()).toBe(false)
    })

    it('shows distance input for relative and absolute modes', async () => {
      wrapper = createWrapper()
      
      const modeSelect = wrapper.find('select')
      
      await modeSelect.setValue('relative')
      const distanceFormGroup1 = wrapper.find('.parameter-grid .form-group:nth-child(2)')
      expect(distanceFormGroup1.exists()).toBe(true)

      await modeSelect.setValue('absolute')
      const distanceFormGroup2 = wrapper.find('.parameter-grid .form-group:nth-child(2)')
      expect(distanceFormGroup2.exists()).toBe(true)
    })
  })

  describe('Individual Screw Controls', () => {
    it('renders individual controls for both screws', () => {
      wrapper = createWrapper()
      
      const screwControls = wrapper.findAll('.bg-gray-50')
      expect(screwControls.length).toBeGreaterThanOrEqual(2)
      
      expect(wrapper.text()).toContain('Screw 0')
      expect(wrapper.text()).toContain('Screw 1')
    })

    it('has default distance values for individual screws', () => {
      wrapper = createWrapper()

      const distanceInputs = wrapper.findAll('input[placeholder="Distance (µm)"]')
      expect(distanceInputs.length).toBe(2)

      distanceInputs.forEach(input => {
        expect(input.element.value).toBe('500')
      })
    })
  })

  describe('Motion API Calls', () => {
    it('calls setBladeScrewsMotion API when collective motion is started', async () => {
      apiService.setBladeScrewsMotion.mockResolvedValue({ data: { success: true } })
      
      wrapper = createWrapper()
      
      const startButton = wrapper.findAll('button').find(btn =>
        btn.text() === 'Start Motion'
      )
      
      await startButton.trigger('click')
      
      expect(apiService.setBladeScrewsMotion).toHaveBeenCalledWith(0, {
        mode: 'relative',
        distance: 1000
      })
    })

    it('calls cancelBladeScrewsMotion API when collective motion is cancelled', async () => {
      apiService.cancelBladeScrewsMotion.mockResolvedValue({ data: { success: true } })
      
      wrapper = createWrapper({
        bladeScrews: [
          { id: 0, position: 1000.0, running: true },
          { id: 1, position: 2000.0, running: false }
        ]
      })
      
      const cancelButton = wrapper.findAll('button').find(btn =>
        btn.text() === 'Cancel Motion'
      )
      
      await cancelButton.trigger('click')
      
      expect(apiService.cancelBladeScrewsMotion).toHaveBeenCalledWith(0)
    })

    it('calls setBladeScrewMotion API when individual screw motion is started', async () => {
      apiService.setBladeScrewMotion.mockResolvedValue({ data: { success: true } })
      
      wrapper = createWrapper()
      
      const moveButtons = wrapper.findAll('button').filter(btn => btn.text() === 'Move')
      await moveButtons[0].trigger('click')
      
      expect(apiService.setBladeScrewMotion).toHaveBeenCalledWith(0, 0, {
        distance: 500
      })
    })

    it('calls cancelBladeScrewMotion API when individual screw motion is cancelled', async () => {
      apiService.cancelBladeScrewMotion.mockResolvedValue({ data: { success: true } })
      
      wrapper = createWrapper({
        bladeScrews: [
          { id: 0, position: 1000.0, running: true },
          { id: 1, position: 2000.0, running: false }
        ]
      })
      
      const stopButtons = wrapper.findAll('button').filter(btn => btn.text() === 'Stop')
      await stopButtons[0].trigger('click')
      
      expect(apiService.cancelBladeScrewMotion).toHaveBeenCalledWith(0, 0)
    })
  })

  describe('Event Emission', () => {
    it('emits motion-started event on successful collective motion start', async () => {
      apiService.setBladeScrewsMotion.mockResolvedValue({ data: { success: true } })
      
      wrapper = createWrapper()
      
      const startButton = wrapper.findAll('button').find(btn =>
        btn.text() === 'Start Motion'
      )
      
      await startButton.trigger('click')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('motion-started')).toBeTruthy()
      expect(wrapper.emitted('motion-started')[0][0]).toEqual({
        drumId: 0,
        type: 'collective',
        motionData: { mode: 'relative', distance: 1000 }
      })
    })

    it('emits motion-cancelled event on successful collective motion cancel', async () => {
      apiService.cancelBladeScrewsMotion.mockResolvedValue({ data: { success: true } })
      
      wrapper = createWrapper({
        bladeScrews: [
          { id: 0, position: 1000.0, running: true },
          { id: 1, position: 2000.0, running: false }
        ]
      })
      
      const cancelButton = wrapper.findAll('button').find(btn =>
        btn.text() === 'Cancel Motion'
      )
      
      await cancelButton.trigger('click')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('motion-cancelled')).toBeTruthy()
      expect(wrapper.emitted('motion-cancelled')[0][0]).toEqual({
        drumId: 0,
        type: 'collective'
      })
    })

    it('emits error event when API call fails', async () => {
      const errorResponse = {
        response: { data: { detail: 'Motion command failed' } }
      }
      apiService.setBladeScrewsMotion.mockRejectedValue(errorResponse)
      
      wrapper = createWrapper()
      
      const startButton = wrapper.findAll('button').find(btn =>
        btn.text() === 'Start Motion'
      )
      
      await startButton.trigger('click')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('error')).toBeTruthy()
      expect(wrapper.emitted('error')[0][0]).toBe(errorResponse)
    })
  })

  describe('Error Handling', () => {
    it('displays error message when API call fails', async () => {
      const errorResponse = {
        response: { data: { detail: 'Motion command failed' } }
      }
      apiService.setBladeScrewsMotion.mockRejectedValue(errorResponse)
      
      wrapper = createWrapper()
      
      const startButton = wrapper.findAll('button').find(btn =>
        btn.text() === 'Start Motion'
      )
      
      await startButton.trigger('click')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Motion command failed')
    })

    it('clears error message after 5 seconds', async () => {
      vi.useFakeTimers()
      
      const errorResponse = {
        response: { data: { detail: 'Test error message' } }
      }
      apiService.setBladeScrewsMotion.mockRejectedValue(errorResponse)
      
      wrapper = createWrapper()
      
      const startButton = wrapper.findAll('button').find(btn =>
        btn.text() === 'Start Motion'
      )
      
      await startButton.trigger('click')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Test error message')
      
      vi.advanceTimersByTime(5000)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).not.toContain('Test error message')
      
      vi.useRealTimers()
    })
  })
})
