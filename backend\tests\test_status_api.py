"""
Tests for Status API Endpoints
==============================

This module contains tests for the status-related API endpoints.
All tests use mocked RecoaterClient to avoid dependencies on hardware.
"""

import pytest
from unittest.mock import Mock
from services.recoater_client import RecoaterConnectionError, RecoaterAPIError

# Create test client

class TestStatusAPI:
    """Test class for status API endpoints."""
    
    def test_get_status_success(self, client, mock_recoater_client):
        """Test successful status retrieval."""
        # Arrange
        mock_recoater_client.get_state.return_value = {
            "state": "ready",
            "timestamp": "2025-07-09T14:30:00Z"
        }
        
        # Act
        response = client.get("/api/v1/status/")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["connected"] is True
        assert data["backend_status"] == "operational"
        assert data["recoater_status"]["state"] == "ready"
        mock_recoater_client.get_state.assert_called_once()
    
    def test_get_status_connection_error(self, client, mock_recoater_client):
        """Test status retrieval when recoater connection fails."""
        # Arrange
        mock_recoater_client.get_state.side_effect = RecoaterConnectionError("Connection failed")
        
        # Act
        response = client.get("/api/v1/status/")
        
        # Assert
        assert response.status_code == 200  # Should still return 200 with error info
        data = response.json()
        assert data["connected"] is False
        assert data["backend_status"] == "operational"
        assert data["recoater_status"] is None
        assert "Failed to connect to recoater hardware" in data["error"]
        mock_recoater_client.get_state.assert_called_once()
    
    def test_get_status_api_error(self, client, mock_recoater_client):
        """Test status retrieval when recoater API returns error."""
        # Arrange
        mock_recoater_client.get_state.side_effect = RecoaterAPIError("API returned status 500")
        
        # Act
        response = client.get("/api/v1/status/")
        
        # Assert
        assert response.status_code == 502
        data = response.json()
        assert "Recoater API error" in data["detail"]
        mock_recoater_client.get_state.assert_called_once()
    
    def test_get_status_unexpected_error(self, client, mock_recoater_client):
        """Test status retrieval when unexpected error occurs."""
        # Arrange
        mock_recoater_client.get_state.side_effect = Exception("Unexpected error")
        
        # Act
        response = client.get("/api/v1/status/")
        
        # Assert
        assert response.status_code == 500
        data = response.json()
        assert "Internal server error" in data["detail"]
        mock_recoater_client.get_state.assert_called_once()
    
    def test_health_check_success(self, client, mock_recoater_client):
        """Test successful health check."""
        # Arrange
        mock_recoater_client.health_check.return_value = True
        
        # Act
        response = client.get("/api/v1/status/health")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["backend_healthy"] is True
        assert data["recoater_healthy"] is True
        assert data["overall_healthy"] is True
        mock_recoater_client.health_check.assert_called_once()
    
    def test_health_check_recoater_unhealthy(self, client, mock_recoater_client):
        """Test health check when recoater is unhealthy."""
        # Arrange
        mock_recoater_client.health_check.return_value = False
        
        # Act
        response = client.get("/api/v1/status/health")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["backend_healthy"] is True
        assert data["recoater_healthy"] is False
        assert data["overall_healthy"] is False
        mock_recoater_client.health_check.assert_called_once()
    
    def test_health_check_error(self, client, mock_recoater_client):
        """Test health check when error occurs."""
        # Arrange
        mock_recoater_client.health_check.side_effect = Exception("Health check failed")
        
        # Act
        response = client.get("/api/v1/status/health")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["backend_healthy"] is True
        assert data["recoater_healthy"] is False
        assert data["overall_healthy"] is False
        assert "Health check failed" in data["error"]
        mock_recoater_client.health_check.assert_called_once()
    
    def test_root_endpoint(self, client):
        """Test the root endpoint."""
        # Act
        response = client.get("/")
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Recoater HMI Backend"
        assert data["version"] == "1.0.0"
        assert data["status"] == "running"
