/**
 * Tests for StatusView Component
 * ==============================
 * 
 * This module contains tests for the StatusView component.
 * All tests use mocked stores and services to avoid dependencies on backend.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import StatusView from '../src/views/StatusView.vue'
import { useStatusStore } from '../src/stores/status'

// Mock the status store
vi.mock('../src/stores/status', () => ({
  useStatusStore: vi.fn()
}))

describe('StatusView', () => {
  let wrapper
  let mockStatusStore

  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia())
    
    // Create mock status store
    mockStatusStore = {
      isConnected: true,
      isHealthy: true,
      lastError: null,
      lastUpdate: new Date('2025-07-09T14:30:00Z'),
      recoaterStatus: { state: 'ready' },
      fetchStatus: vi.fn().mockResolvedValue({ state: 'ready' })
    }
    
    // Mock the useStatusStore function
    useStatusStore.mockReturnValue(mockStatusStore)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('renders correctly with basic structure', () => {
    // Act
    wrapper = mount(StatusView)
    
    // Assert
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.status-view').exists()).toBe(true)
    expect(wrapper.find('.view-title').exists()).toBe(true)
    expect(wrapper.find('.view-title').text()).toBe('System Status')
    expect(wrapper.find('.status-cards').exists()).toBe(true)
  })

  it('displays connection status correctly when connected', () => {
    // Arrange
    mockStatusStore.isConnected = true
    mockStatusStore.isHealthy = true
    
    // Act
    wrapper = mount(StatusView)
    
    // Assert
    const connectionCard = wrapper.find('.status-card')
    expect(connectionCard.exists()).toBe(true)
    
    const statusItems = connectionCard.findAll('.status-item')
    expect(statusItems.length).toBeGreaterThan(0)
    
    // Check backend status
    const backendStatusItem = statusItems.find(item => 
      item.find('.status-label').text().includes('Backend')
    )
    expect(backendStatusItem.exists()).toBe(true)
    expect(backendStatusItem.find('.status-value').text()).toBe('Connected')
    expect(backendStatusItem.find('.status-value').classes()).toContain('status-value--success')
  })

  it('displays connection status correctly when disconnected', () => {
    // Arrange
    mockStatusStore.isConnected = false
    mockStatusStore.isHealthy = false
    
    // Act
    wrapper = mount(StatusView)
    
    // Assert
    const connectionCard = wrapper.find('.status-card')
    const statusItems = connectionCard.findAll('.status-item')
    
    // Check backend status
    const backendStatusItem = statusItems.find(item => 
      item.find('.status-label').text().includes('Backend')
    )
    expect(backendStatusItem.find('.status-value').text()).toBe('Disconnected')
    expect(backendStatusItem.find('.status-value').classes()).toContain('status-value--error')
  })

  it('displays recoater status correctly when healthy', () => {
    // Arrange
    mockStatusStore.isConnected = true
    mockStatusStore.isHealthy = true
    mockStatusStore.recoaterStatus = { state: 'ready' }
    
    // Act
    wrapper = mount(StatusView)
    
    // Assert
    const connectionCard = wrapper.find('.status-card')
    const statusItems = connectionCard.findAll('.status-item')
    
    // Check recoater status
    const recoaterStatusItem = statusItems.find(item => 
      item.find('.status-label').text().includes('Recoater')
    )
    expect(recoaterStatusItem.exists()).toBe(true)
    expect(recoaterStatusItem.find('.status-value').text()).toBe('Connected')
    expect(recoaterStatusItem.find('.status-value').classes()).toContain('status-value--success')
  })

  it('displays system information when recoater data is available', () => {
    // Arrange
    mockStatusStore.recoaterStatus = { state: 'ready' }
    
    // Act
    wrapper = mount(StatusView)
    
    // Assert
    const cards = wrapper.findAll('.status-card')
    const systemInfoCard = cards.find(card => 
      card.find('.card-title').text().includes('System Information')
    )
    expect(systemInfoCard.exists()).toBe(true)
    
    const statusItems = systemInfoCard.findAll('.status-item')
    const stateItem = statusItems.find(item => 
      item.find('.status-label').text().includes('State')
    )
    expect(stateItem.exists()).toBe(true)
    expect(stateItem.find('.status-value').text()).toBe('ready')
  })

  it('displays no data message when recoater data is unavailable', () => {
    // Arrange
    mockStatusStore.recoaterStatus = null
    
    // Act
    wrapper = mount(StatusView)
    
    // Assert
    const cards = wrapper.findAll('.status-card')
    const systemInfoCard = cards.find(card => 
      card.find('.card-title').text().includes('System Information')
    )
    expect(systemInfoCard.exists()).toBe(true)
    
    const noDataMessage = systemInfoCard.find('.status-value--muted')
    expect(noDataMessage.exists()).toBe(true)
    expect(noDataMessage.text()).toBe('No recoater data available')
  })

  it('displays error information when there is an error', () => {
    // Arrange
    mockStatusStore.lastError = 'Connection timeout'
    
    // Act
    wrapper = mount(StatusView)
    
    // Assert
    const cards = wrapper.findAll('.status-card')
    const errorCard = cards.find(card => 
      card.find('.card-title').text().includes('Error Information')
    )
    expect(errorCard.exists()).toBe(true)
    
    const errorValue = errorCard.find('.status-value--error')
    expect(errorValue.exists()).toBe(true)
    expect(errorValue.text()).toBe('Connection timeout')
  })

  it('does not display error card when there is no error', () => {
    // Arrange
    mockStatusStore.lastError = null

    // Act
    wrapper = mount(StatusView)

    // Assert
    const cards = wrapper.findAll('.status-card')
    const errorCard = cards.find(card =>
      card.find('.card-title').text().includes('Error Information')
    )
    expect(errorCard).toBeUndefined()
  })

  it('calls fetchStatus on mount', () => {
    // Act
    wrapper = mount(StatusView)
    
    // Assert
    expect(mockStatusStore.fetchStatus).toHaveBeenCalledOnce()
  })

  it('has refresh button that calls fetchStatus', async () => {
    // Arrange
    wrapper = mount(StatusView)
    const refreshButton = wrapper.find('.btn--primary')
    
    // Act
    await refreshButton.trigger('click')
    
    // Assert
    expect(mockStatusStore.fetchStatus).toHaveBeenCalledTimes(2) // Once on mount, once on click
  })

  it('shows refresh button with correct initial state', async () => {
    // Arrange
    wrapper = mount(StatusView)
    const refreshButton = wrapper.find('.btn--primary')

    // Assert
    expect(refreshButton.exists()).toBe(true)
    expect(refreshButton.text()).toBe('Refresh Status')
    expect(refreshButton.attributes('disabled')).toBeUndefined()
  })
})
