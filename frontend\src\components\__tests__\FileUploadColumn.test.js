import { mount } from '@vue/test-utils'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import FileUploadColumn from '@/components/FileUploadColumn.vue'

describe('FileUploadColumn', () => {
  let wrapper

  const defaultProps = {
    drumId: 0,
    isConnected: true,
    isLoading: false
  }

  beforeEach(() => {
    wrapper = mount(FileUploadColumn, {
      props: defaultProps
    })
  })

  describe('Component Rendering', () => {
    it('renders with correct drum title', () => {
      expect(wrapper.find('.card-title').text()).toBe('Drum 0')
    })

    it('renders for different drum IDs', async () => {
      await wrapper.setProps({ drumId: 1 })
      expect(wrapper.find('.card-title').text()).toBe('Drum 1')

      await wrapper.setProps({ drumId: 2 })
      expect(wrapper.find('.card-title').text()).toBe('Drum 2')
    })

    it('shows disabled overlay when not connected', async () => {
      await wrapper.setProps({ isConnected: false })
      expect(wrapper.find('.disabled-overlay').exists()).toBe(true)
      expect(wrapper.find('.disabled-text').text()).toBe('Connect to recoater to manage files')
    })

    it('hides disabled overlay when connected', () => {
      expect(wrapper.find('.disabled-overlay').exists()).toBe(false)
    })

    it('renders file input with correct attributes', () => {
      const fileInput = wrapper.find('input[type="file"]')
      expect(fileInput.exists()).toBe(true)
      expect(fileInput.attributes('accept')).toBe('.png,.cli,image/png,application/octet-stream')
      expect(fileInput.attributes('id')).toBe('file-input-0')
    })

    it('renders all three buttons', () => {
      const buttons = wrapper.findAll('button')
      expect(buttons).toHaveLength(3)
      
      const uploadBtn = buttons.find(btn => btn.text().includes('Upload'))
      const downloadBtn = buttons.find(btn => btn.text().includes('Download'))
      const deleteBtn = buttons.find(btn => btn.text().includes('Delete'))
      
      expect(uploadBtn).toBeTruthy()
      expect(downloadBtn).toBeTruthy()
      expect(deleteBtn).toBeTruthy()
    })
  })

  describe('File Selection', () => {
    it('handles file selection correctly', async () => {
      const file = new File(['test content'], 'test.png', { type: 'image/png' })
      const fileInput = wrapper.find('input[type="file"]')
      
      Object.defineProperty(fileInput.element, 'files', {
        value: [file],
        writable: false
      })
      
      await fileInput.trigger('change')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.selectedFile).toBe(file)
      expect(wrapper.find('.file-info').exists()).toBe(true)
      expect(wrapper.find('.file-info').text()).toContain('test.png')
    })

    it('validates file types correctly', async () => {
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})
      const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      const fileInput = wrapper.find('input[type="file"]')
      
      Object.defineProperty(fileInput.element, 'files', {
        value: [invalidFile],
        writable: false
      })
      
      await fileInput.trigger('change')
      
      expect(alertSpy).toHaveBeenCalledWith('Please select a PNG or CLI file.')
      expect(wrapper.vm.selectedFile).toBeNull()
      
      alertSpy.mockRestore()
    })

    it('accepts CLI files with correct extension', async () => {
      const cliFile = new File(['cli content'], 'test.cli', { type: 'application/octet-stream' })
      const fileInput = wrapper.find('input[type="file"]')
      
      Object.defineProperty(fileInput.element, 'files', {
        value: [cliFile],
        writable: false
      })
      
      await fileInput.trigger('change')
      
      expect(wrapper.vm.selectedFile).toBe(cliFile)
    })

    it('clears file selection', async () => {
      // First select a file
      const file = new File(['test'], 'test.png', { type: 'image/png' })
      wrapper.vm.selectedFile = file
      await wrapper.vm.$nextTick()
      
      // Then clear it
      wrapper.vm.clearFileSelection()
      
      expect(wrapper.vm.selectedFile).toBeNull()
    })
  })

  describe('Button States and Behavior', () => {
    it('disables buttons when not connected', async () => {
      await wrapper.setProps({ isConnected: false })
      
      const buttons = wrapper.findAll('button')
      buttons.forEach(button => {
        expect(button.attributes('disabled')).toBeDefined()
      })
    })

    it('disables buttons when loading', async () => {
      await wrapper.setProps({ isLoading: true })
      
      const buttons = wrapper.findAll('button')
      buttons.forEach(button => {
        expect(button.attributes('disabled')).toBeDefined()
      })
    })

    it('disables upload button when no file is selected', () => {
      const uploadBtn = wrapper.findAll('button').find(btn => btn.text().includes('Upload'))
      expect(uploadBtn.attributes('disabled')).toBeDefined()
    })

    it('enables upload button when file is selected and connected', async () => {
      const file = new File(['test'], 'test.png', { type: 'image/png' })
      wrapper.vm.selectedFile = file
      await wrapper.vm.$nextTick()
      
      const uploadBtn = wrapper.findAll('button').find(btn => btn.text().includes('Upload'))
      expect(uploadBtn.attributes('disabled')).toBeUndefined()
    })

    it('shows loading text during operations', async () => {
      await wrapper.setProps({ isLoading: true })
      
      const buttons = wrapper.findAll('button')
      const uploadBtn = buttons.find(btn => btn.text().includes('Uploading'))
      const downloadBtn = buttons.find(btn => btn.text().includes('Downloading'))
      const deleteBtn = buttons.find(btn => btn.text().includes('Deleting'))
      
      expect(uploadBtn).toBeTruthy()
      expect(downloadBtn).toBeTruthy()
      expect(deleteBtn).toBeTruthy()
    })
  })

  describe('Event Emission', () => {
    it('emits upload event with correct data', async () => {
      const file = new File(['test'], 'test.png', { type: 'image/png' })
      wrapper.vm.selectedFile = file
      await wrapper.vm.$nextTick()
      
      const uploadBtn = wrapper.findAll('button').find(btn => btn.text().includes('Upload'))
      await uploadBtn.trigger('click')
      
      expect(wrapper.emitted('upload')).toBeTruthy()
      expect(wrapper.emitted('upload')[0][0]).toEqual({
        drumId: 0,
        file: file
      })
    })

    it('emits download event with correct data', async () => {
      const downloadBtn = wrapper.findAll('button').find(btn => btn.text().includes('Download'))
      await downloadBtn.trigger('click')
      
      expect(wrapper.emitted('download')).toBeTruthy()
      expect(wrapper.emitted('download')[0][0]).toEqual({
        drumId: 0
      })
    })

    it('emits delete event with correct data after confirmation', async () => {
      const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true)
      
      const deleteBtn = wrapper.findAll('button').find(btn => btn.text().includes('Delete'))
      await deleteBtn.trigger('click')
      
      expect(wrapper.emitted('delete')).toBeTruthy()
      expect(wrapper.emitted('delete')[0][0]).toEqual({
        drumId: 0
      })
      
      confirmSpy.mockRestore()
    })

    it('does not emit delete event when confirmation is cancelled', async () => {
      const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(false)
      
      const deleteBtn = wrapper.findAll('button').find(btn => btn.text().includes('Delete'))
      await deleteBtn.trigger('click')
      
      expect(wrapper.emitted('delete')).toBeFalsy()
      
      confirmSpy.mockRestore()
    })
  })

  describe('Clear Selection Button', () => {
    it('shows clear button only when file is selected', async () => {
      expect(wrapper.find('.clear-btn').exists()).toBe(false)
      
      const file = new File(['test'], 'test.png', { type: 'image/png' })
      wrapper.vm.selectedFile = file
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.clear-btn').exists()).toBe(true)
    })

    it('clears selection when clear button is clicked', async () => {
      const file = new File(['test'], 'test.png', { type: 'image/png' })
      wrapper.vm.selectedFile = file
      await wrapper.vm.$nextTick()
      
      const clearBtn = wrapper.find('.clear-btn')
      await clearBtn.trigger('click')
      
      expect(wrapper.vm.selectedFile).toBeNull()
    })

    it('clears file selection after successful upload', async () => {
      const file = new File(['test'], 'test.png', { type: 'image/png' })
      wrapper.vm.selectedFile = file
      
      wrapper.vm.uploadFile()
      
      expect(wrapper.vm.selectedFile).toBeNull()
    })
  })

  describe('File Size Formatting', () => {
    it('formats file sizes correctly', () => {
      expect(wrapper.vm.formatFileSize(0)).toBe('0 Bytes')
      expect(wrapper.vm.formatFileSize(1024)).toBe('1 KB')
      expect(wrapper.vm.formatFileSize(1048576)).toBe('1 MB')
      expect(wrapper.vm.formatFileSize(1073741824)).toBe('1 GB')
      expect(wrapper.vm.formatFileSize(500)).toBe('500 Bytes')
    })
  })

  describe('Accessibility', () => {
    it('has proper labels for form elements', () => {
      const label = wrapper.find('label[for="file-input-0"]')
      expect(label.exists()).toBe(true)
      expect(label.text()).toBe('Geometry File (PNG or CLI):')
    })

    it('uses proper button classes for styling hierarchy', () => {
      const buttons = wrapper.findAll('button')
      const uploadBtn = buttons.find(btn => btn.text().includes('Upload'))
      const downloadBtn = buttons.find(btn => btn.text().includes('Download'))
      const deleteBtn = buttons.find(btn => btn.text().includes('Delete'))
      
      expect(uploadBtn.classes()).toContain('btn-primary')
      expect(downloadBtn.classes()).toContain('btn-secondary')
      expect(deleteBtn.classes()).toContain('btn-tertiary')
      expect(deleteBtn.classes()).toContain('btn-danger')
    })
  })

  describe('Prop Validation', () => {
    it('validates drumId prop correctly', () => {
      const validator = FileUploadColumn.props.drumId.validator
      expect(validator(0)).toBe(true)
      expect(validator(1)).toBe(true)
      expect(validator(2)).toBe(true)
      expect(validator(3)).toBe(false)
      expect(validator(-1)).toBe(false)
    })
  })
})
