<template>
  <div class="control-card">
    <!-- Card Header -->
    <div class="card-header">
      <h3 class="card-title">Drum {{ drumId }}</h3>
      <div class="status-indicator">
        <div 
          :class="[
            'status-dot',
            drumStatus?.running ? 'status-running' : 'status-stopped'
          ]"
        ></div>
        <span class="status-text">{{ drumStatus?.running ? 'Running' : 'Stopped' }}</span>
      </div>
    </div>

    <div class="card-content">
      <!-- Drum Information -->
      <div class="info-grid">
        <div class="info-box">
          <label class="info-label">Position</label>
          <div class="info-value">{{ drumStatus?.position?.toFixed(2) || '0.00' }} mm</div>
        </div>
        <div class="info-box">
          <label class="info-label">Circumference</label>
          <div class="info-value">{{ drumStatus?.circumference?.toFixed(2) || '0.00' }} mm</div>
        </div>
      </div>

      <!-- Motion Controls -->
      <div class="control-group">
        <h4 class="group-title">Motion Control</h4>
        <div class="parameter-grid">
          <div class="form-group">
            <label class="form-label">Mode</label>
            <select v-model="motionParams.mode" class="form-select" :disabled="!connected || drumStatus?.running">
              <option value="relative">Relative</option>
              <option value="absolute">Absolute</option>
              <option value="turns">Turns</option>
              <option value="speed">Speed</option>
              <option value="homing">Homing</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">Speed (mm/s)</label>
            <input v-model.number="motionParams.speed" type="number" min="0.1" step="0.1" class="form-input" :disabled="!connected || drumStatus?.running"/>
          </div>
          <div v-if="motionParams.mode === 'relative' || motionParams.mode === 'absolute'" class="form-group">
            <label class="form-label">Distance (mm)</label>
            <input v-model.number="motionParams.distance" type="number" step="0.1" class="form-input" :disabled="!connected || drumStatus?.running"/>
          </div>
          <div v-if="motionParams.mode === 'turns'" class="form-group">
            <label class="form-label">Turns</label>
            <input v-model.number="motionParams.turns" type="number" min="0.1" step="0.1" class="form-input" :disabled="!connected || drumStatus?.running"/>
          </div>
        </div>
        <div class="button-row">
          <button @click="startMotion" :disabled="!connected || drumStatus?.running" class="btn btn-primary">Start Motion</button>
          <button @click="cancelMotion" :disabled="!connected || !drumStatus?.running" class="btn btn-danger">Cancel Motion</button>
        </div>
      </div>

      <!-- Pressure Controls -->
      <div class="info-grid">
        <!-- Ejection Pressure -->
        <div class="pressure-box bg-blue-50 border-blue-200">
          <h5 class="group-title text-blue-800">Ejection Pressure</h5>
          <div class="pressure-readings">
            <span>Current: {{ ejectionData?.value?.toFixed(1) || '0.0' }} {{ ejectionData?.unit || 'Pa' }}</span>
            <span>Target: {{ ejectionData?.target?.toFixed(1) || '0.0' }} {{ ejectionData?.unit || 'Pa' }}</span>
          </div>
          <div class="pressure-set">
            <input v-model.number="ejectionTarget" type="number" min="0" step="1" class="form-input-sm" :disabled="!connected" placeholder="Target"/>
            <select v-model="ejectionUnit" class="form-select-sm" :disabled="!connected">
              <option value="pascal">Pa</option>
              <option value="bar">bar</option>
            </select>
            <button @click="setEjectionPressure" :disabled="!connected" class="btn-set bg-blue-500 hover:bg-blue-600">Set</button>
          </div>
        </div>

        <!-- Suction Pressure -->
        <div class="pressure-box bg-green-50 border-green-200">
          <h5 class="group-title text-green-800">Suction Pressure</h5>
          <div class="pressure-readings">
            <span>Current: {{ suctionData?.value?.toFixed(1) || '0.0' }} Pa</span>
            <span>Target: {{ suctionData?.target?.toFixed(1) || '0.0' }} Pa</span>
          </div>
          <div class="pressure-set">
            <input v-model.number="suctionTarget" type="number" min="0" step="0.1" class="form-input-sm" :disabled="!connected" placeholder="Target"/>
            <button @click="setSuctionPressure" :disabled="!connected" class="btn-set bg-green-500 hover:bg-green-600">Set</button>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="errorMessage" class="error-message mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';
import apiService from '../services/api';

export default {
  name: 'DrumControl',
  props: {
    drumId: { type: Number, required: true },
    drumStatus: { type: Object, default: () => ({}) },
    motionData: { type: Object, default: () => ({}) },
    ejectionData: { type: Object, default: () => ({}) },
    suctionData: { type: Object, default: () => ({}) },
    connected: { type: Boolean, default: false }
  },
  emits: ['error', 'success', 'motion-started', 'motion-cancelled', 'pressure-set'],
  setup(props, { emit }) {
    const motionParams = ref({
      mode: 'relative',
      speed: 30.0,
      distance: 100.0,
      turns: 1.0
    });
    const ejectionTarget = ref(200.0);
    const ejectionUnit = ref('pascal');
    const suctionTarget = ref(2.0);
    const errorMessage = ref('');
    const errorTimeout = ref(null);

    const showError = (error) => {
      const message = error?.response?.data?.detail || error?.message || 'An error occurred';
      errorMessage.value = message;
      if (errorTimeout.value) {
        clearTimeout(errorTimeout.value);
      }
      errorTimeout.value = setTimeout(() => {
        errorMessage.value = '';
      }, 5000);
    };

    const startMotion = async () => {
      try {
        const motionData = {
          mode: motionParams.value.mode,
          speed: motionParams.value.speed
        };
        if (motionParams.value.mode === 'relative' || motionParams.value.mode === 'absolute') {
          motionData.distance = motionParams.value.distance;
        } else if (motionParams.value.mode === 'turns') {
          motionData.turns = motionParams.value.turns;
        }
        await apiService.setDrumMotion(props.drumId, motionData);
        emit('motion-started', { drumId: props.drumId, motionData });
        emit('success', `Motion started for Drum ${props.drumId}`);
      } catch (error) {
        showError(error);
        emit('error', error);
      }
    };

    const cancelMotion = async () => {
      try {
        await apiService.cancelDrumMotion(props.drumId);
        emit('motion-cancelled', { drumId: props.drumId });
        emit('success', `Motion cancelled for Drum ${props.drumId}`);
      } catch (error) {
        showError(error);
        emit('error', error);
      }
    };

    const setEjectionPressure = async () => {
      try {
        const ejectionData = {
          target: ejectionTarget.value,
          unit: ejectionUnit.value
        };
        await apiService.setDrumEjection(props.drumId, ejectionData);
        emit('pressure-set', { drumId: props.drumId, type: 'ejection', value: ejectionTarget.value });
        emit('success', `Ejection pressure set for Drum ${props.drumId}`);
      } catch (error) {
        showError(error);
        emit('error', error);
      }
    };

    const setSuctionPressure = async () => {
      try {
        const suctionData = { target: suctionTarget.value };
        await apiService.setDrumSuction(props.drumId, suctionData);
        emit('pressure-set', { drumId: props.drumId, type: 'suction', value: suctionTarget.value });
        emit('success', `Suction pressure set for Drum ${props.drumId}`);
      } catch (error) {
        showError(error);
        emit('error', error);
      }
    };

    return {
      motionParams,
      ejectionTarget,
      ejectionUnit,
      suctionTarget,
      errorMessage,
      startMotion,
      cancelMotion,
      setEjectionPressure,
      setSuctionPressure,
    };
  }
};
</script>

<style scoped>
/* Card styles inspired by PrintView */
.control-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
}
.control-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}
.card-title { font-size: 1.1rem; font-weight: 600; color: #1f2937; }

.status-indicator { display: flex; align-items: center; gap: 0.5rem; }
.status-dot { width: 10px; height: 10px; border-radius: 50%; }
.status-running { background-color: #10b981; }
.status-stopped { background-color: #6b7280; }
.status-text { font-size: 0.875rem; color: #4b5563; font-weight: 500; }

.card-content { padding: 0rem 1rem 1rem 1rem; }

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}
.info-box { background-color: #f9fafb; padding: 0.75rem; border-radius: 6px; border: 1px solid #f3f4f6; }
.info-label { display: block; font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem; text-transform: uppercase; letter-spacing: 0.05em; }
.info-value { font-size: 1rem; font-weight: 500; color: #1f2937; }

.control-group { margin-bottom: 1.5rem; }
.group-title { font-size: 1rem; font-weight: 600; color: #374151; margin-bottom: 1rem; }

.parameter-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem; margin-bottom: 1rem; }
.form-group { display: flex; flex-direction: column; }
.form-label { font-size: 0.875rem; color: #4b5563; margin-bottom: 0.25rem; }
.form-input, .form-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
  font-size: 0.875rem;
}
.form-input:disabled, .form-select:disabled { background-color: #f3f4f6; cursor: not-allowed; }

.button-row { display: flex; gap: 0.5rem; }
.btn { flex: 1; padding: 0.6rem; border-radius: 6px; font-weight: 500; cursor: pointer; border: 1px solid transparent; transition: background-color 0.2s; }
.btn-set {
  background-color: var(--color-primary) !important;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.4rem 0.8rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}
.btn-set:hover:not(:disabled) {
  background-color: var(--color-primary-hover) !important;
}
.btn:disabled { opacity: 0.5; cursor: not-allowed; }
.btn-primary { background-color: #3b82f6; color: white; }
.btn-primary:hover:not(:disabled) { background-color: #2563eb; }
.btn-danger { background-color: #ef4444; color: white; }
.btn-danger:hover:not(:disabled) { background-color: #dc2626; }

.pressure-box { padding: 1rem; border-radius: 6px; border: 1px solid; }
.pressure-readings { font-size: 0.8rem; color: #4b5563; margin-bottom: 0.75rem; display: flex; flex-direction: column; gap: 0.25rem; }
.pressure-set { display: flex; gap: 0.5rem; align-items: center; }
.form-input-sm { flex-grow: 1; width: 60px; padding: 0.4rem; border: 1px solid #d1d5db; border-radius: 4px; font-size: 0.875rem; }
.form-select-sm { padding: 0.4rem; border: 1px solid #d1d5db; border-radius: 4px; font-size: 0.875rem; }
.btn-set { color: white; border: none; border-radius: 4px; padding: 0.3rem 0.7rem; cursor: pointer; font-weight: 480; transition: background-color 0.2s; }
.btn-set:disabled { opacity: 0.5; cursor: not-allowed; }
</style>