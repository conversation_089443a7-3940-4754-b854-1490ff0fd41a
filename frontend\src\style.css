/**
 * Global Styles for Recoater HMI
 * ===============================
 */

/* Design System Variables */
:root {
  /* Color Tokens */
  --color-primary: #3498db;
  --color-primary-hover: #2980b9;
  --color-primary-disabled: #a8cee8;
  --color-success: #27ae60;
  --color-success-hover: #229954;
  --color-success-disabled: #82c49a;
  --color-danger: #e74c3c;
  --color-danger-hover: #c0392b;
  --color-danger-disabled: #f1a99d;
  --color-neutral-100: #f8f9fa;
  --color-neutral-200: #e9ecef;
  --color-neutral-300: #dee2e6;
  --color-neutral-400: #ced4da;
  --color-neutral-500: #adb5bd;
  --color-neutral-600: #6c757d;
  --color-neutral-700: #495057;
  --color-neutral-800: #343a40;
  --color-neutral-900: #212529;

  /* Typography Scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.95rem;
  --font-size-base: 1rem;
  --font-size-md: 1.4rem;
  --font-size-lg: 1.8rem;
  --font-size-xl: 2.25rem;

  /* Spacing Tokens */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-base: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  color: #2c3e50;
  line-height: 1.6;
}

#app {
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

/* Typography Utility Classes */
.heading-xl {
  font-size: var(--font-size-xl);
  font-weight: 600;
  line-height: 1.1;
  color: var(--color-neutral-800);
}

.heading-lg {
  font-size: var(--font-size-lg);
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-neutral-800);
}

.heading-md {
  font-size: var(--font-size-md);
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-neutral-800);
}

.label-sm {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-neutral-700);
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-muted {
  color: var(--color-neutral-600);
}

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-base); }
.mb-4 { margin-bottom: var(--spacing-md); }

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-base); }
.mt-4 { margin-top: var(--spacing-md); }

/* Form elements */
input, select, textarea, button {
  font-family: inherit;
  font-size: inherit;
}

input, select, textarea {
  padding: 0.5rem;
  border: 1px solid var(--color-neutral-400);
  border-radius: 4px;
  background-color: white;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Button styles */
.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  background-color: var(--color-neutral-600);
  color: white;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: var(--font-size-base);
  line-height: 1.4;
}

.btn:hover {
  background-color: var(--color-neutral-700);
}

.btn-primary {
  background-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-secondary:hover {
  background-color: var(--color-primary);
  color: white;
}

.btn-tertiary {
  background-color: transparent;
  color: var(--color-neutral-600);
  border: none;
  padding: 0.25rem 0.5rem;
}

.btn-tertiary:hover {
  color: var(--color-neutral-800);
  background-color: var(--color-neutral-100);
}

.btn-success {
  background-color: var(--color-success);
}

.btn-success:hover {
  background-color: var(--color-success-hover);
}

.btn-danger {
  background-color: var(--color-danger);
}

.btn-danger:hover {
  background-color: var(--color-danger-hover);
}

.btn:disabled {
  background-color: #d1d5db !important;
  color: #9ca3af !important;
  cursor: not-allowed;
  opacity: 1;
  border: 1px solid #e5e7eb;
}

.btn:disabled:hover {
  background-color: #d1d5db !important;
  color: #9ca3af !important;
  transform: none;
}

/* Card styles */
.card {
  background: white;
  border-radius: 8px;
  padding: var(--spacing-lg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--color-neutral-300);
  margin-bottom: var(--spacing-lg);
}

.card-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--color-neutral-300);
}

.card-title {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--color-neutral-800);
}

/* Grid system */
.grid {
  display: grid;
  gap: var(--spacing-md);
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}

.grid-3-equal {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
}

/* Responsive design */
@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-3-equal,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .app-main {
    flex-direction: column;
  }
  
  .app-nav {
    width: 100%;
  }
  
  .nav-list {
    display: flex;
    overflow-x: auto;
  }
  
  .nav-item {
    margin-bottom: 0;
    margin-right: 0.5rem;
    white-space: nowrap;
  }
}
