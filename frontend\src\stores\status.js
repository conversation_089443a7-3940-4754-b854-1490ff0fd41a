/**
 * Status Store
 * ============
 * 
 * Pinia store for managing the application's connection status and real-time
 * updates from the backend via WebSocket.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '../services/api'

export const useStatusStore = defineStore('status', () => {
  // State
  const isConnected = ref(false)
  const recoaterStatus = ref(null)
  const axisData = ref(null)
  const drumData = ref(null)
  const levelerData = ref(null)
  const printData = ref(null)
  const lastError = ref(null)
  const lastUpdate = ref(null)
  const websocket = ref(null)
  const currentPage = ref('status') // Track current page for selective data fetching
  const subscribedDataTypes = ref(new Set(['status'])) // Always subscribe to basic status
  
  // Computed
  const isHealthy = computed(() => {
    return isConnected.value && recoaterStatus.value && !lastError.value
  })

  const connected = computed(() => isConnected.value)
  const backendHealthy = computed(() => isConnected.value && !lastError.value)
  
  // Actions
  function updateStatus(statusData) {
    recoaterStatus.value = statusData
    lastUpdate.value = new Date()
    lastError.value = null
  }

  function updateAxisData(newAxisData) {
    axisData.value = newAxisData
    lastUpdate.value = new Date()
  }

  function updateDrumData(data) {
    drumData.value = data
    lastUpdate.value = new Date()
  }

  function updateLevelerData(data) {
    levelerData.value = data
    lastUpdate.value = new Date()
  }

  function updatePrintData(data) {
    printData.value = data
    lastUpdate.value = new Date()
  }
  
  function setError(error) {
    lastError.value = error
    lastUpdate.value = new Date()
  }
  
  function setConnectionStatus(connected) {
    isConnected.value = connected
    if (!connected) {
      recoaterStatus.value = null
      lastError.value = 'Connection lost'
    }
  }

  // Page-aware data management
  function setCurrentPage(page) {
    currentPage.value = page
    updateDataSubscriptions()
  }

  function updateDataSubscriptions() {
    const newSubscriptions = new Set(['status']) // Always include basic status

    // Add page-specific data types
    switch (currentPage.value) {
      case 'axis':
        // newSubscriptions.add('axis')
        break
      case 'recoater':
        newSubscriptions.add('drum')
        newSubscriptions.add('leveler')
        break
      case 'print':
        newSubscriptions.add('print')
        newSubscriptions.add('drum') // Print page also shows drum info
        break
      case 'config':
        // Config page might need all data for comprehensive view
        //newSubscriptions.add('axis')
        //newSubscriptions.add('drum')
        //newSubscriptions.add('leveler')
        //newSubscriptions.add('print')
        break
      case 'status':
        // Example: Add drum data to status page
        // newSubscriptions.add('drum')
        break
      // Default: only basic status
    }

    subscribedDataTypes.value = newSubscriptions

    // If WebSocket is connected, send subscription update
    if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
      websocket.value.send(JSON.stringify({
        type: 'subscribe',
        data_types: Array.from(newSubscriptions)
      }))
    }
  }

  // Manual override function for testing/debugging
  function setManualSubscriptions(dataTypes) {
    subscribedDataTypes.value = new Set(['status', ...dataTypes])

    // If WebSocket is connected, send subscription update
    if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
      websocket.value.send(JSON.stringify({
        type: 'subscribe',
        data_types: Array.from(subscribedDataTypes.value)
      }))
    }
  }
  
  async function fetchStatus() {
    try {
      const response = await apiService.getStatus()
      updateStatus(response.data)
      setConnectionStatus(true)
      return response.data
    } catch (error) {
      console.error('Failed to fetch status:', error)
      setError(error.message || 'Failed to fetch status')
      setConnectionStatus(false)
      throw error
    }
  }
  
  function connectWebSocket() {
    if (websocket.value) {
      return // Already connected
    }
    
    const wsUrl = `ws://localhost:8000/ws`
    console.log('Connecting to WebSocket:', wsUrl)
    
    try {
      websocket.value = new WebSocket(wsUrl)
      
      websocket.value.onopen = () => {
        console.log('WebSocket connected')
        setConnectionStatus(true)
        // Send initial subscription based on current page
        updateDataSubscriptions()
      }
      
      websocket.value.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          console.log('WebSocket message received:', message)
          
          if (message.type === 'status_update') {
            updateStatus(message.data)
            if (message.axis_data) {
              updateAxisData(message.axis_data)
            }
            if (message.drum_data) {
              updateDrumData(message.drum_data)
            }
            if (message.leveler_data) {
              updateLevelerData(message.leveler_data)
            }
            if (message.print_data) {
              updatePrintData(message.print_data)
            }
          } else if (message.type === 'connection_error') {
            setError(message.error)
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }
      
      websocket.value.onclose = () => {
        console.log('WebSocket disconnected')
        setConnectionStatus(false)
        websocket.value = null
        
        // Attempt to reconnect after 3 seconds
        setTimeout(() => {
          if (!websocket.value) {
            connectWebSocket()
          }
        }, 3000)
      }
      
      websocket.value.onerror = (error) => {
        console.error('WebSocket error:', error)
        setError('WebSocket connection error')
      }
      
    } catch (error) {
      console.error('Failed to create WebSocket:', error)
      setError('Failed to establish WebSocket connection')
    }
  }
  
  function disconnectWebSocket() {
    if (websocket.value) {
      websocket.value.close()
      websocket.value = null
    }
    setConnectionStatus(false)
  }
  
  return {
    // State
    isConnected,
    recoaterStatus,
    axisData,
    drumData,
    levelerData,
    printData,
    lastError,
    lastUpdate,
    currentPage,
    subscribedDataTypes,

    // Computed
    isHealthy,
    connected,
    backendHealthy,

    // Actions
    updateStatus,
    updateAxisData,
    updateDrumData,
    updateLevelerData,
    updatePrintData,
    setError,
    setConnectionStatus,
    setCurrentPage,
    updateDataSubscriptions,
    setManualSubscriptions,
    fetchStatus,
    connectWebSocket,
    disconnectWebSocket
  }
})
