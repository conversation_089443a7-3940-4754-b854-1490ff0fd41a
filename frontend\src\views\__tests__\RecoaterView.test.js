import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ref } from 'vue'
import { createPinia, setActivePinia } from 'pinia'
import RecoaterView from '../RecoaterView.vue'
import DrumControl from '../../components/DrumControl.vue'
import { useStatusStore } from '../../stores/status'

// Mock the status store
vi.mock('../../stores/status', () => ({
  useStatusStore: vi.fn()
}))

// Mock DrumControl component
vi.mock('../../components/DrumControl.vue', () => ({
  default: {
    name: 'DrumControl',
    props: ['drumId', 'drumStatus', 'motionData', 'ejectionData', 'suctionData', 'connected'],
    emits: ['error', 'motion-started', 'motion-cancelled', 'pressure-set'],
    template: '<div class="drum-control-mock" data-testid="drum-control">Drum {{ drumId }}</div>'
  }
}))

describe('RecoaterView', () => {
  let wrapper
  let mockStatusStore

  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    setActivePinia(createPinia())

    vi.clearAllMocks()

    // Create mock status store with reactive properties
    mockStatusStore = {
      isConnected: false,
      drumData: null,
      lastError: null,
      connectWebSocket: vi.fn(),
      disconnectWebSocket: vi.fn()
    }

    // Mock the useStatusStore function
    useStatusStore.mockReturnValue(mockStatusStore)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  const createWrapper = () => {
    return mount(RecoaterView, {
      global: {
        components: {
          DrumControl
        }
      }
    })
  }

  describe('Component Rendering', () => {
    it('renders the main title and description', () => {
      wrapper = createWrapper()

      expect(wrapper.find('h1').text()).toBe('Recoater Dashboard')
      expect(wrapper.text()).toContain('Monitor and control all recoater hardware components from this central dashboard.')
    })

    it('displays connection status section', () => {
      wrapper = createWrapper()
      
      expect(wrapper.text()).toContain('Connection Status')
      expect(wrapper.text()).toContain('Disconnected')
    })

    it('shows connected status when connected', () => {
      mockStatusStore.isConnected = true
      wrapper = createWrapper()

      expect(wrapper.text()).toContain('Connected')
    })

    it('displays error message when there is a connection error', () => {
      mockStatusStore.lastError = 'Connection timeout'
      wrapper = createWrapper()

      expect(wrapper.text()).toContain('Connection timeout')
    })
  })

  describe('Drum Data Display', () => {
    it('shows loading state when not connected and no drum data', () => {
      wrapper = createWrapper()

      expect(wrapper.text()).toContain('Connecting...')
      expect(wrapper.text()).toContain('Attempting to connect to the recoater backend.')
    })

    it('shows no drums available message when connected but no drum data', () => {
      mockStatusStore.isConnected = true
      mockStatusStore.drumData = null
      wrapper = createWrapper()

      expect(wrapper.text()).toContain('No Drums Available')
      expect(wrapper.text()).toContain('No drum data is currently available from the recoater')
    })

    it('renders drum controls when drum data is available', () => {
      mockStatusStore.isConnected = true
      mockStatusStore.drumData = {
        '0': {
          info: { running: false, position: 100.0 },
          motion: { mode: 'relative', speed: 30.0 },
          ejection: { target: 200.0, value: 150.0 },
          suction: { target: 3.0, value: 2.5 }
        },
        '1': {
          info: { running: true, position: 200.0 },
          motion: { mode: 'speed', speed: 50.0 },
          ejection: { target: 250.0, value: 200.0 },
          suction: { target: 4.0, value: 3.5 }
        }
      }
      wrapper = createWrapper()
      
      const drumControls = wrapper.findAll('[data-testid="drum-control"]')
      expect(drumControls).toHaveLength(2)

      // Check that drum controls are rendered with correct drum IDs
      expect(drumControls[0].text()).toContain('Drum 0')
      expect(drumControls[1].text()).toContain('Drum 1')
    })
  })

  describe('WebSocket Lifecycle', () => {
    it('does not manually connect to WebSocket on mount (managed globally)', () => {
      wrapper = createWrapper()

      // WebSocket connection is now managed globally, not by individual components
      expect(mockStatusStore.connectWebSocket).not.toHaveBeenCalled()
    })

    it('disconnects from WebSocket on unmount', () => {
      wrapper = createWrapper()
      wrapper.unmount()
      
      expect(mockStatusStore.disconnectWebSocket).toHaveBeenCalledOnce()
    })
  })

  describe('Event Handling', () => {
    beforeEach(() => {
      mockStatusStore.isConnected = true
      mockStatusStore.drumData = {
        '0': {
          info: { running: false, position: 100.0 },
          motion: { mode: 'relative', speed: 30.0 },
          ejection: { target: 200.0, value: 150.0 },
          suction: { target: 3.0, value: 2.5 }
        }
      }
      wrapper = createWrapper()
    })

    it('handles drum error events', async () => {
      const drumControl = wrapper.findComponent(DrumControl)
      const errorEvent = {
        response: { data: { detail: 'Motion command failed' } }
      }
      
      await drumControl.vm.$emit('error', errorEvent)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Motion command failed')
    })

    it('handles motion started events', async () => {
      const drumControl = wrapper.findComponent(DrumControl)
      const motionEvent = { drumId: 0 }
      
      await drumControl.vm.$emit('motion-started', motionEvent)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Motion started for Drum 0')
    })

    it('handles motion cancelled events', async () => {
      const drumControl = wrapper.findComponent(DrumControl)
      const motionEvent = { drumId: 0 }
      
      await drumControl.vm.$emit('motion-cancelled', motionEvent)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Motion cancelled for Drum 0')
    })

    it('handles pressure set events', async () => {
      const drumControl = wrapper.findComponent(DrumControl)
      const pressureEvent = { drumId: 0, type: 'ejection' }
      
      await drumControl.vm.$emit('pressure-set', pressureEvent)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('ejection pressure set for Drum 0')
    })
  })

  describe('Notification Management', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('auto-clears error messages after 5 seconds', async () => {
      vi.useFakeTimers()
      
      // Trigger an error
      wrapper.vm.globalError = 'Test error message'
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Test error message')
      
      // Fast-forward time
      vi.advanceTimersByTime(5000)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).not.toContain('Test error message')
      
      vi.useRealTimers()
    })

    it('auto-clears success messages after 3 seconds', async () => {
      vi.useFakeTimers()
      
      // Trigger a success message
      wrapper.vm.successMessage = 'Test success message'
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Test success message')
      
      // Fast-forward time
      vi.advanceTimersByTime(3000)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).not.toContain('Test success message')
      
      vi.useRealTimers()
    })

    it('allows manual dismissal of error messages', async () => {
      wrapper.vm.globalError = 'Test error message'
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Test error message')
      
      // Find and click the dismiss button
      const dismissButton = wrapper.find('.message-error .close-btn')
      await dismissButton.trigger('click')
      
      expect(wrapper.text()).not.toContain('Test error message')
    })

    it('allows manual dismissal of success messages', async () => {
      wrapper.vm.successMessage = 'Test success message'
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Test success message')
      
      // Find and click the dismiss button
      const dismissButton = wrapper.find('.message-success .close-btn')
      await dismissButton.trigger('click')
      
      expect(wrapper.text()).not.toContain('Test success message')
    })
  })

  describe('Responsive Layout', () => {
    it('uses responsive grid classes for drum controls', () => {
      mockStatusStore.isConnected = true
      mockStatusStore.drumData = {
        '0': { info: {}, motion: {}, ejection: {}, suction: {} }
      }
      wrapper = createWrapper()

      const drumGrid = wrapper.find('.dashboard-grid')
      expect(drumGrid.exists()).toBe(true)
    })
  })
})
