import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import DrumControl from '../DrumControl.vue'
import apiService from '../../services/api'

// Mock the API service
vi.mock('../../services/api', () => ({
  default: {
    setDrumMotion: vi.fn(),
    cancelDrumMotion: vi.fn(),
    setDrumEjection: vi.fn(),
    setDrumSuction: vi.fn()
  }
}))

describe('DrumControl', () => {
  let wrapper

  const defaultProps = {
    drumId: 0,
    drumStatus: {
      running: false,
      position: 100.5,
      circumference: 314.16
    },
    motionData: {
      mode: 'relative',
      speed: 30.0,
      distance: 100.0
    },
    ejectionData: {
      maximum: 300.0,
      target: 200.0,
      value: 150.0,
      unit: 'pascal'
    },
    suctionData: {
      maximum: 5.0,
      target: 3.0,
      value: 2.5
    },
    connected: true
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(DrumControl, {
      props: { ...defaultProps, ...props }
    })
  }

  describe('Component Rendering', () => {
    it('renders drum control card with correct drum ID', () => {
      wrapper = createWrapper()
      expect(wrapper.find('h3').text()).toBe('Drum 0')
    })

    it('displays drum status correctly', () => {
      wrapper = createWrapper()
      expect(wrapper.text()).toContain('100.50 mm')
      expect(wrapper.text()).toContain('314.16 mm')
      expect(wrapper.text()).toContain('Stopped')
    })

    it('shows running status when drum is running', () => {
      wrapper = createWrapper({
        drumStatus: { ...defaultProps.drumStatus, running: true }
      })
      expect(wrapper.text()).toContain('Running')
    })

    it('displays ejection pressure data', () => {
      wrapper = createWrapper()
      expect(wrapper.text()).toContain('Current: 150.0 pascal')
      expect(wrapper.text()).toContain('Target: 200.0 pascal')
    })

    it('displays suction pressure data', () => {
      wrapper = createWrapper()
      expect(wrapper.text()).toContain('Current: 2.5 Pa')
      expect(wrapper.text()).toContain('Target: 3.0 Pa')
    })
  })

  describe('Motion Controls', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('has motion mode selector with correct options', () => {
      const modeSelect = wrapper.find('select')
      const options = modeSelect.findAll('option')
      
      expect(options).toHaveLength(5)
      expect(options[0].text()).toBe('Relative')
      expect(options[1].text()).toBe('Absolute')
      expect(options[2].text()).toBe('Turns')
      expect(options[3].text()).toBe('Speed')
      expect(options[4].text()).toBe('Homing')
    })

    it('shows distance input for relative mode', async () => {
      await wrapper.find('select').setValue('relative')
      expect(wrapper.find('input[type="number"][step="0.1"]').exists()).toBe(true)
    })

    it('shows turns input for turns mode', async () => {
      await wrapper.find('select').setValue('turns')
      // Find the turns input by looking for the label text and then finding the input
      const labels = wrapper.findAll('label')
      const turnsLabel = labels.find(label => label.text().includes('Turns'))
      expect(turnsLabel).toBeTruthy()
      const turnsInput = turnsLabel.element.parentElement.querySelector('input')
      expect(turnsInput).toBeTruthy()
    })

    it('disables controls when not connected', () => {
      wrapper = createWrapper({ connected: false })
      
      const inputs = wrapper.findAll('input')
      const selects = wrapper.findAll('select')
      const buttons = wrapper.findAll('button')
      
      inputs.forEach(input => {
        expect(input.attributes('disabled')).toBeDefined()
      })
      selects.forEach(select => {
        expect(select.attributes('disabled')).toBeDefined()
      })
      buttons.forEach(button => {
        expect(button.attributes('disabled')).toBeDefined()
      })
    })

    it('disables start motion button when drum is running', () => {
      wrapper = createWrapper({
        drumStatus: { ...defaultProps.drumStatus, running: true }
      })

      const buttons = wrapper.findAll('button')
      const startButton = buttons.find(btn => btn.text().includes('Start Motion'))
      expect(startButton.attributes('disabled')).toBeDefined()
    })

    it('disables cancel motion button when drum is not running', () => {
      const buttons = wrapper.findAll('button')
      const cancelButton = buttons.find(btn => btn.text().includes('Cancel Motion'))
      expect(cancelButton.attributes('disabled')).toBeDefined()
    })
  })

  describe('Motion API Calls', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('calls setDrumMotion API when start motion is clicked', async () => {
      apiService.setDrumMotion.mockResolvedValue({ data: { status: 'motion_created' } })

      // Set motion parameters
      await wrapper.find('select').setValue('relative')

      // Find inputs by their position and context - speed input is the first number input
      const numberInputs = wrapper.findAll('input[type="number"]')
      await numberInputs[0].setValue(50.0) // Speed input
      await numberInputs[1].setValue(100.0) // Distance input (appears after selecting 'relative')

      // Click start motion
      const buttons = wrapper.findAll('button')
      const startButton = buttons.find(btn => btn.text().includes('Start Motion'))
      await startButton.trigger('click')

      expect(apiService.setDrumMotion).toHaveBeenCalledWith(0, {
        mode: 'relative',
        speed: 50.0,
        distance: 100.0
      })
    })

    it('calls cancelDrumMotion API when cancel motion is clicked', async () => {
      wrapper = createWrapper({
        drumStatus: { ...defaultProps.drumStatus, running: true }
      })
      
      apiService.cancelDrumMotion.mockResolvedValue({ data: { status: 'motion_cancelled' } })
      
      const buttons = wrapper.findAll('button')
      const cancelButton = buttons.find(btn => btn.text().includes('Cancel Motion'))
      await cancelButton.trigger('click')
      
      expect(apiService.cancelDrumMotion).toHaveBeenCalledWith(0)
    })

    it('emits motion-started event on successful motion start', async () => {
      apiService.setDrumMotion.mockResolvedValue({ data: { status: 'motion_created' } })
      
      const buttons = wrapper.findAll('button')
      const startButton = buttons.find(btn => btn.text().includes('Start Motion'))
      await startButton.trigger('click')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('motion-started')).toBeTruthy()
      expect(wrapper.emitted('motion-started')[0][0]).toEqual({
        drumId: 0,
        motionData: { mode: 'relative', speed: 30.0, distance: 100.0 }
      })
    })

    it('emits motion-cancelled event on successful motion cancel', async () => {
      wrapper = createWrapper({
        drumStatus: { ...defaultProps.drumStatus, running: true }
      })
      
      apiService.cancelDrumMotion.mockResolvedValue({ data: { status: 'motion_cancelled' } })
      
      const buttons = wrapper.findAll('button')
      const cancelButton = buttons.find(btn => btn.text().includes('Cancel Motion'))
      await cancelButton.trigger('click')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('motion-cancelled')).toBeTruthy()
      expect(wrapper.emitted('motion-cancelled')[0][0]).toEqual({ drumId: 0 })
    })
  })

  describe('Pressure Controls', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('calls setDrumEjection API when ejection pressure is set', async () => {
      apiService.setDrumEjection.mockResolvedValue({ data: { status: 'ejection_set' } })
      
      // Find ejection controls
      const ejectionSection = wrapper.find('.bg-blue-50')
      const ejectionInput = ejectionSection.find('input[type="number"]')
      const ejectionSelect = ejectionSection.find('select')
      const setButton = ejectionSection.find('button')
      
      await ejectionInput.setValue(250.0)
      await ejectionSelect.setValue('bar')
      await setButton.trigger('click')
      
      expect(apiService.setDrumEjection).toHaveBeenCalledWith(0, {
        target: 250.0,
        unit: 'bar'
      })
    })

    it('calls setDrumSuction API when suction pressure is set', async () => {
      apiService.setDrumSuction.mockResolvedValue({ data: { status: 'suction_set' } })
      
      // Find suction controls
      const suctionSection = wrapper.find('.bg-green-50')
      const suctionInput = suctionSection.find('input[type="number"]')
      const setButton = suctionSection.find('button')
      
      await suctionInput.setValue(4.5)
      await setButton.trigger('click')
      
      expect(apiService.setDrumSuction).toHaveBeenCalledWith(0, {
        target: 4.5
      })
    })

    it('emits pressure-set event on successful pressure setting', async () => {
      apiService.setDrumEjection.mockResolvedValue({ data: { status: 'ejection_set' } })
      
      const ejectionSection = wrapper.find('.bg-blue-50')
      const setButton = ejectionSection.find('button')
      
      await setButton.trigger('click')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.emitted('pressure-set')).toBeTruthy()
      expect(wrapper.emitted('pressure-set')[0][0]).toEqual({
        drumId: 0,
        type: 'ejection',
        value: 200.0
      })
    })
  })

  describe('Error Handling', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('displays error message when API call fails', async () => {
      const errorResponse = {
        response: { data: { detail: 'Motion command failed' } }
      }
      apiService.setDrumMotion.mockRejectedValue(errorResponse)
      
      const buttons = wrapper.findAll('button')
      const startButton = buttons.find(btn => btn.text().includes('Start Motion'))
      await startButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(wrapper.text()).toContain('Motion command failed')
    })

    it('emits error event when API call fails', async () => {
      const errorResponse = {
        response: { data: { detail: 'Motion command failed' } }
      }
      apiService.setDrumMotion.mockRejectedValue(errorResponse)

      const buttons = wrapper.findAll('button')
      const startButton = buttons.find(btn => btn.text().includes('Start Motion'))
      await startButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(wrapper.emitted('error')).toBeTruthy()
      expect(wrapper.emitted('error')[0][0]).toEqual(errorResponse)
    })

    it('clears error message after 5 seconds', async () => {
      vi.useFakeTimers()

      const errorResponse = {
        response: { data: { detail: 'Motion command failed' } }
      }
      apiService.setDrumMotion.mockRejectedValue(errorResponse)

      const buttons = wrapper.findAll('button')
      const startButton = buttons.find(btn => btn.text().includes('Start Motion'))
      await startButton.trigger('click')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('Motion command failed')
      
      vi.advanceTimersByTime(5000)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).not.toContain('Motion command failed')
      
      vi.useRealTimers()
    })
  })
})
