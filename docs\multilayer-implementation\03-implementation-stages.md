# Implementation Stages: Multi-Layer Print Job System

## Implementation Overview

The multi-layer print job system will be implemented in four sequential stages, each building upon the previous stage. This approach ensures:
- **Incremental Progress**: Each stage delivers testable functionality
- **Risk Mitigation**: Issues can be identified and resolved early
- **Minimal Disruption**: Existing functionality remains operational
- **Clear Validation**: Each stage has specific success criteria

**Multi-Material System Requirements:**
- **3 Drum Configuration**: The recoater system uses 3 separate drums for multi-material printing
- **3 CLI Files per Job**: Each multi-layer job requires 3 separate CLI files (one for each drum)
- **Coordinated Layer Processing**: All 3 drums must be uploaded with corresponding layer data before each layer print
- **Synchronized Material Deposition**: Each layer requires coordinated deposition from all 3 drums

## Stage 1: OPC UA Infrastructure

### Objectives
Establish FastAPI backend as OPC UA server to host coordination variables for TwinCAT PLC client, providing simplified real-time coordination.

### Prerequisites
- TwinCAT PLC configured as OPC UA client
- Network connectivity between backend and PLC
- Backend hosting OPC UA server on accessible endpoint

### Implementation Tasks

#### 1.1 Install OPC UA Dependencies
```bash
# Install required Python packages
pip install asyncua>=1.0.0
pip install asyncio-mqtt  # Optional: for additional messaging
```

#### 1.2 Create OPC UA Configuration Module
**File**: `backend/app/config/opcua_config.py`

**Key Components:**
- Server endpoint configuration
- Variable node definitions for hosting
- Security settings and certificates
- Server startup and shutdown configurations

#### 1.3 Implement OPC UA Server Manager
**File**: `backend/app/services/opcua_server.py`

**Core Functions:**
- OPC UA server hosting and management
- Variable hosting and updates
- Subscription handling for real-time events
- Error handling and logging

#### 1.4 Create Basic Variable Interface
**Implementation:**
```python
class OPCUACoordinator:
    async def connect(self) -> bool
    async def disconnect(self) -> bool
    async def write_variable(self, name: str, value: Any) -> bool
    async def read_variable(self, name: str) -> Any
    async def subscribe_to_changes(self, variables: List[str]) -> bool
```

#### 1.5 Basic Integration Testing
**Test Cases:**
- OPC UA connection establishment
- Variable read/write operations
- Subscription functionality
- Connection recovery after network interruption

### Success Criteria
- [ ] OPC UA server starts and hosts variables successfully
- [ ] PLC can connect as client and read variables
- [ ] Variables can be updated from backend
- [ ] Server recovery works after network interruptions
- [ ] No impact on existing system functionality

### Deliverables
- OPC UA client implementation
- Configuration management
- Basic testing framework
- Connection monitoring and logging

---

## Stage 2: Multi-Material Job Management

### Objectives
Implement multi-material job state management with 3-drum coordination, CLI file processing for multiple drums, and basic job lifecycle management without PLC coordination.

### Prerequisites
- Stage 1 completed successfully
- Existing CLI parsing functionality available
- RecoaterClient working for single-layer operations
- Understanding of 3-drum material configuration

### Implementation Tasks

#### 2.1 Create Multi-Material Job State Management
**File**: `backend/app/models/multilayer_job.py`

**Key Components:**
```python
@dataclass
class MultiMaterialJobState:
    job_id: str
    file_ids: Dict[int, str]  # drum_id -> file_id mapping
    total_layers: int
    current_layer: int
    is_active: bool
    remaining_layers: Dict[int, List[LayerData]]  # drum_id -> layer data
    start_time: float
    # Process state tracking
    waiting_for_print_start: bool
    waiting_for_layer_complete: bool
    waiting_for_galvo: bool
    # Drum coordination
    drums_ready: Dict[int, bool]  # drum_id -> ready status
    drums_uploaded: Dict[int, bool]  # drum_id -> upload status
```

#### 2.2 Enhance CLI File Processing for Multi-Material
**Modifications to**: `backend/app/services/cli_parser.py`

**New Capabilities:**
- Layer-by-layer extraction from 3 separate CLI files
- Multi-drum CLI coordination and validation
- Ensure all 3 CLI files have same layer count
- Single-layer CLI generation for each drum
- Layer data caching and management per drum
- Progress tracking and validation across all drums

#### 2.3 Implement Multi-Material API Endpoints
**File**: `backend/app/api/print.py` (Enhanced)

**New Endpoints:**
```python
@router.post("/cli/start-multimaterial-job")  # Accept 3 file_ids
@router.get("/multimaterial-job/status") 
@router.post("/multimaterial-job/cancel")
@router.post("/multimaterial-job/clear-error")
@router.get("/multimaterial-job/drum-status/{drum_id}")
```

#### 2.4 Create Multi-Material Job Management Service
**File**: `backend/app/services/multimaterial_job_manager.py`

**Core Functions:**
- Job initialization with 3 CLI files validation
- Multi-drum layer sequence management
- Progress tracking across all drums
- Error state management per drum
- Coordinated layer upload management

#### 2.5 Implement Multi-Material Testing Framework
**Test Cases:**
- Multi-material job creation with 3 CLI files
- CLI file parsing and layer extraction per drum
- Layer count validation across all drums
- Job status tracking for all drums
- Cancel and error handling operations
- Drum coordination and readiness checks

### Success Criteria
- [ ] Multi-material jobs can be created and initialized with 3 CLI files
- [ ] All 3 CLI files are correctly parsed into individual layers
- [ ] Layer count validation ensures all drums have same layer count
- [ ] Job status is accurately tracked and reported for all drums
- [ ] Jobs can be cancelled and cleaned up properly across all drums
- [ ] Error states are properly managed per drum
- [ ] API endpoints respond correctly with multi-drum data

### Deliverables
- Multi-material job state management (3-drum coordination)
- Enhanced CLI file processing for multi-drum validation
- New API endpoints for multi-material job management
- Multi-drum job lifecycle management service
- Comprehensive testing suite for 3-drum operations

---

## Stage 3: Coordination Logic Implementation

### Objectives
Implement the core coordination logic that orchestrates between Aerosint server, PLC, and MCP server for automated multi-material layer progression across 3 drums.

### Prerequisites
- Stage 1 and 2 completed successfully
- OPC UA communication fully functional
- Multi-material job management operational
- All 3 drums configured and accessible

### Implementation Tasks

#### 3.1 Implement Multi-Drum Aerosint Status Monitoring
**Enhancement to**: `backend/app/services/recoater_client.py`

**New Capabilities:**
- Continuous status polling for all 3 drums during print operations
- State transition detection per drum ("printing" → "ready")
- Multi-drum error state handling and recovery
- Timeout management for stuck operations per drum
- Drum-specific status reporting and coordination

#### 3.2 Create Multi-Material Coordination State Machine
**File**: `backend/app/services/coordination_engine.py`

**Core Components:**
```python
class MultiMaterialCoordinationEngine:
    async def start_multimaterial_job(self, job_state) -> bool
    async def process_layer_cycle_all_drums(self) -> bool
    async def upload_layer_to_all_drums(self, layer_num) -> bool
    async def wait_for_all_drums_ready(self) -> bool
    async def handle_layer_completion(self) -> bool
    async def handle_galvo_completion(self) -> bool
    async def handle_error_state(self, error, drum_id=None) -> bool
```

#### 3.3 Implement Event-Driven Multi-Drum Layer Progression
**Process Flow:**
1. **Multi-Drum Layer Upload**: Send next layer to all 3 drums simultaneously
2. **All-Drums-Ready Check**: Ensure all 3 drums report "ready" status before proceeding
3. **Print Monitoring**: Wait for all drums to show "printing" status
4. **PLC Coordination**: Signal PLC when all drums are ready to start
5. **Completion Detection**: Monitor for all drums to return to "ready" status
6. **Galvo Coordination**: Wait for galvo scan completion after all drums complete
7. **Layer Progression**: Move to next layer or complete job

#### 3.4 Enhanced Multi-Drum Error Handling and Recovery
**Error Scenarios:**
- Individual drum communication failures
- Partial drum upload failures (some drums succeed, others fail)
- PLC communication timeouts
- Layer upload failures on specific drums
- State synchronization errors between drums
- Hardware fault conditions on individual drums
- Drum-specific timeout handling

#### 3.5 Implement Multi-Material Coordination Testing
**Test Scenarios:**
- Complete multi-material job execution with 3 drums
- Error injection per drum and recovery testing
- Partial failure scenarios (1-2 drums fail)
- Timeout handling validation per drum
- State synchronization verification across all drums
- Performance and timing tests with 3-drum coordination

### Success Criteria
- [ ] Complete automation of multi-material printing across 3 drums
- [ ] Simplified coordination with minimal PLC dependency
- [ ] Robust error handling with backend_error and plc_error flags
- [ ] Correct layer progression without manual intervention
- [ ] Backend-driven layer progression based on Aerosint status polling
- [ ] System handles error conditions gracefully per drum
- [ ] All 3 drums coordinate properly for each layer

### Deliverables
- Complete multi-material coordination engine implementation
- Enhanced Aerosint integration for 3-drum operation
- Event-driven layer progression logic for multi-drum coordination
- Comprehensive error handling per drum
- Full integration testing suite for multi-material operation

---

## Stage 4: Frontend Integration and UI Enhancement

### Objectives
Integrate multi-material functionality into the Vue.js frontend, providing operators with intuitive controls and real-time monitoring capabilities for 3-drum coordination.

### Prerequisites
- Stages 1-3 completed successfully
- Backend coordination fully operational
- Multi-material API endpoints functional
- 3-drum system accessible

### Implementation Tasks

#### 4.1 Create Multi-Material Job Components
**New Vue.js Components:**
- `MultiMaterialJobControl.vue` - Job start/stop controls for 3-drum operation
- `JobProgressDisplay.vue` - Real-time progress monitoring across all drums
- `DrumStatusIndicator.vue` - Individual drum status and coordination display
- `MultiDrumErrorPanel.vue` - Error messages and recovery options per drum
- `FileUploadMultiDrum.vue` - CLI file upload interface for 3 files

#### 4.2 Enhance State Management for Multi-Material
**Pinia Store Enhancement**: `stores/printJobStore.js`

**New State:**
```javascript
state: {
  multiMaterialJob: {
    isActive: false,
    jobId: null,
    currentLayer: 0,
    totalLayers: 0,
    progressPercentage: 0,
    status: 'idle',
    errorMessage: '',
    drums: {
      0: { fileId: null, ready: false, uploaded: false, status: 'idle' },
      1: { fileId: null, ready: false, uploaded: false, status: 'idle' },
      2: { fileId: null, ready: false, uploaded: false, status: 'idle' }
    }
  }
}
```

#### 4.3 Implement Real-Time Multi-Drum Status Updates
**Features:**
- WebSocket integration for live updates from all drums
- Automatic status polling fallback for each drum
- Progress bar with layer information and drum coordination
- State indicators for waiting conditions per drum
- All-drums-ready indicator

#### 4.4 Create Multi-Material Operator Interface
**UI Elements:**
- **Start Multi-Material Job**: Button to begin automated 3-drum printing
- **File Upload Section**: Upload interface for 3 CLI files (one per drum)
- **Drum Status Grid**: Visual feedback for each drum's status
- **Progress Display**: Current layer, total layers, percentage complete
- **Emergency Controls**: Cancel job, clear errors per drum
- **Job History**: Previous multi-material jobs and their outcomes
- **Drum Health Monitoring**: Individual drum error states and recovery options

#### 4.5 Integrate with Existing Interface
**Modifications:**
- Disable conflicting single-layer controls during multi-material jobs
- Integrate multi-material status into main dashboard
- Maintain existing functionality for single-layer operations
- Add multi-material options to file upload interface
- Create drum selection and file assignment interface

#### 4.6 Implement Frontend Testing for Multi-Material
**Test Cases:**
- Component rendering and interaction for 3-drum interface
- Real-time status update handling per drum
- Error display and recovery workflows per drum
- Integration with existing UI components
- Multi-file upload validation and assignment
- Responsive design on touch interfaces for complex multi-drum data

### Success Criteria
- [ ] Intuitive operator interface for multi-material jobs with 3-drum coordination
- [ ] Real-time progress monitoring and status display for all drums
- [ ] Proper integration with existing UI components
- [ ] Error handling with backend_error and plc_error distinction
- [ ] Clear Error Flags button for operator error recovery
- [ ] Touch-friendly interface suitable for production use with complex multi-drum data
- [ ] All UI components tested and validated for 3-drum operation
- [ ] File upload interface correctly handles 3 CLI files

### Deliverables
- Complete frontend multi-material integration
- New Vue.js components for 3-drum job management
- Enhanced state management and real-time updates for multi-drum operation
- Integrated operator interface for multi-material printing
- Frontend testing suite for multi-material operation

---

## Risk Management

### Technical Risks and Mitigation

| Risk | Probability | Impact | Mitigation Strategy |
|------|-------------|--------|-------------------|
| OPC UA communication issues | Medium | High | Extensive testing, fallback mechanisms |
| PLC integration complexity | Medium | High | Early PLC team involvement, incremental testing |
| Multi-drum state synchronization failures | High | High | Comprehensive state validation, per-drum error recovery |
| Performance degradation with 3-drum coordination | Medium | Medium | Performance testing, optimization, parallel processing |
| CLI file validation complexity | Medium | Medium | Robust validation, clear error messaging |

### Implementation Risks and Mitigation

| Risk | Probability | Impact | Mitigation Strategy |
|------|-------------|--------|-------------------|
| Multi-drum coordination complexity | High | High | Incremental testing, modular approach per drum |
| Integration challenges with 3-drum system | Medium | High | Early integration testing, parallel development |
| Testing complexity for multi-material | High | Medium | Automated testing, clear acceptance criteria per drum |
| Production disruption | Low | High | Parallel deployment, rollback procedures |

## Quality Assurance

### Testing Strategy by Stage

**Stage 1 Testing:**
- Unit tests for OPC UA client functions
- Integration tests with PLC simulator
- Connection reliability testing
- Performance benchmarking

**Stage 2 Testing:**
- API endpoint testing for 3-drum operations
- Multi-material job state management validation
- CLI file processing accuracy for all drums
- Error handling verification per drum
- Layer count validation across 3 files

**Stage 3 Testing:**
- End-to-end coordination testing with 3 drums
- Error injection and recovery per drum
- Performance under load with multi-drum operation
- Timing and synchronization validation across all drums
- Partial failure scenario testing

**Stage 4 Testing:**
- UI component testing for multi-drum interface
- User workflow validation for 3-drum operation
- Cross-browser compatibility with complex interface
- Touch interface usability for multi-material data

### Acceptance Criteria

Each stage must meet specific acceptance criteria before proceeding:

1. **All automated tests pass** (including multi-drum scenarios)
2. **Manual testing scenarios completed** (3-drum coordination)
3. **Performance requirements met** (multi-drum operation)
4. **Error handling validated** (per-drum error states)
5. **Documentation updated** (multi-material requirements)
6. **Code review completed** (3-drum implementation)

## Resource Requirements

### Development Team
- **Backend Developer**: OPC UA integration, multi-drum coordination logic
- **Frontend Developer**: Vue.js components, multi-drum UI integration
- **PLC Engineer**: TwinCAT configuration, variable setup for 3-drum operation
- **Test Engineer**: Integration testing, multi-material validation
- **DevOps Engineer**: Deployment, monitoring setup

### Infrastructure Requirements
- **Development Environment**: PLC simulator, OPC UA test server, 3-drum simulator
- **Testing Environment**: Complete 3-drum system replica
- **Staging Environment**: Production-like setup with 3 drums
- **Monitoring Tools**: Logging, performance monitoring for multi-drum operation

### External Dependencies
- **PLC Team**: OPC UA server configuration for 3-drum coordination
- **Hardware Team**: Recoater and MCP server integration with 3-drum setup
- **Operations Team**: Testing support and validation for multi-material operation

This staged implementation approach ensures systematic progress while maintaining system stability and providing clear validation points throughout the development process, specifically addressing the multi-material requirements with 3-drum coordination.
