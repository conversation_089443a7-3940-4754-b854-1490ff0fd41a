"""
Tests for Configuration API Router
===================================

This module contains tests for the configuration API endpoints.
All tests use mocked RecoaterClient to avoid hardware dependencies.
"""

import pytest
from services.recoater_client import RecoaterConnectionError, RecoaterAPIError


class TestConfigurationAPI:
    """Test class for configuration API endpoints."""

    def test_get_configuration_success(self, client, mock_recoater_client):
        """Test successful configuration retrieval."""
        # The mock is already configured in conftest.py with the correct config
        # Make request
        response = client.get("/api/v1/config/")

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["build_space_diameter"] == 250.0
        assert data["build_space_dimensions"]["length"] == 250.0
        assert data["build_space_dimensions"]["width"] == 96.0
        assert data["ejection_matrix_size"] == 192
        assert data["gaps"] == [130.0, 130.0]
        assert data["resolution"] == 500
        mock_recoater_client.get_config.assert_called_once()

    def test_get_configuration_connection_error(self, client, mock_recoater_client):
        """Test configuration retrieval with connection error."""
        # Setup mock to raise connection error
        mock_recoater_client.get_config.side_effect = RecoaterConnectionError("Connection failed")

        # Make request
        response = client.get("/api/v1/config/")

        # Assertions
        assert response.status_code == 503
        assert "Cannot connect to recoater" in response.json()["detail"]

    def test_get_configuration_api_error(self, client, mock_recoater_client):
        """Test configuration retrieval with API error."""
        # Setup mock to raise API error
        mock_recoater_client.get_config.side_effect = RecoaterAPIError("API error")

        # Make request
        response = client.get("/api/v1/config/")

        # Assertions
        assert response.status_code == 502
        assert "Recoater API error" in response.json()["detail"]

    def test_set_configuration_success(self, client, mock_recoater_client):
        """Test successful configuration update."""
        # Reset the mock to clear any previous calls
        mock_recoater_client.reset_mock()

        # Prepare request data
        config_data = {
            "build_space_diameter": 300.0,
            "build_space_dimensions": {
                "length": 300.0,
                "width": 100.0
            },
            "ejection_matrix_size": 200,
            "gaps": [140.0, 140.0],
            "resolution": 400
        }

        # Make request
        response = client.put("/api/v1/config/", json=config_data)

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Configuration updated successfully"
        assert data["config"] == config_data
        mock_recoater_client.set_config.assert_called_once_with(config_data)

    def test_set_configuration_partial_update(self, client, mock_recoater_client):
        """Test configuration update with partial data."""
        # Reset the mock to clear any previous calls
        mock_recoater_client.reset_mock()

        # Prepare partial request data
        config_data = {
            "build_space_diameter": 300.0,
            "resolution": 400
        }

        # Make request
        response = client.put("/api/v1/config/", json=config_data)

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["config"] == config_data
        mock_recoater_client.set_config.assert_called_once_with(config_data)

    def test_set_configuration_connection_error(self, client, mock_recoater_client):
        """Test configuration update with connection error."""
        # Setup mock to raise connection error
        mock_recoater_client.set_config.side_effect = RecoaterConnectionError("Connection failed")

        # Prepare request data
        config_data = {"build_space_diameter": 300.0}

        # Make request
        response = client.put("/api/v1/config/", json=config_data)

        # Assertions
        assert response.status_code == 503
        assert "Cannot connect to recoater" in response.json()["detail"]

    def test_set_configuration_api_error(self, client, mock_recoater_client):
        """Test configuration update with API error."""
        # Setup mock to raise API error
        mock_recoater_client.set_config.side_effect = RecoaterAPIError("API error")

        # Prepare request data
        config_data = {"build_space_diameter": 300.0}

        # Make request
        response = client.put("/api/v1/config/", json=config_data)

        # Assertions
        assert response.status_code == 502
        assert "Recoater API error" in response.json()["detail"]

    def test_set_configuration_validation_error(self, client, mock_recoater_client):
        """Test configuration update with invalid data."""
        # Prepare invalid request data (negative values)
        config_data = {
            "build_space_diameter": -100.0,
            "resolution": -50
        }

        # Make request
        response = client.put("/api/v1/config/", json=config_data)

        # Assertions
        assert response.status_code == 422  # Validation error
        mock_recoater_client.set_config.assert_not_called()

    def test_set_configuration_invalid_dimensions(self, client, mock_recoater_client):
        """Test configuration update with invalid build space dimensions."""
        # Prepare invalid request data (missing required fields)
        config_data = {
            "build_space_dimensions": {
                "length": 250.0
                # Missing required 'width' field
            }
        }

        # Make request
        response = client.put("/api/v1/config/", json=config_data)

        # Assertions
        assert response.status_code == 422  # Validation error
        mock_recoater_client.set_config.assert_not_called()
