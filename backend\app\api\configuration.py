"""
Configuration API Router
=========================

This module provides the recoater configuration API endpoints for the Recoater HMI.
It handles requests for getting and setting the main recoater configuration.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import logging

from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError
from app.dependencies import get_recoater_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/config", tags=["configuration"])

# Pydantic models for request/response validation

class BuildSpaceDimensions(BaseModel):
    """Model for build space dimensions."""
    length: float = Field(..., ge=0, description="The length of the build space [mm]")
    width: float = Field(..., ge=0, description="The width of the build space [mm]")

class ConfigurationRequest(BaseModel):
    """Request model for recoater configuration."""
    build_space_diameter: Optional[float] = Field(None, ge=0, description="The diameter of the build space [mm]")
    build_space_dimensions: Optional[BuildSpaceDimensions] = Field(None, description="Build space dimensions")
    ejection_matrix_size: Optional[int] = Field(None, ge=0, description="The number of points in the ejection matrix")
    gaps: Optional[List[float]] = Field(None, description="The list of gaps between the drums [mm]")
    resolution: Optional[int] = Field(None, ge=0, description="The resolution of the recoater, the size of one pixel [µm]")

class ConfigurationResponse(BaseModel):
    """Response model for recoater configuration."""
    build_space_diameter: Optional[float] = Field(None, description="The diameter of the build space [mm]")
    build_space_dimensions: Optional[BuildSpaceDimensions] = Field(None, description="Build space dimensions")
    ejection_matrix_size: Optional[int] = Field(None, description="The number of points in the ejection matrix")
    gaps: Optional[List[float]] = Field(None, description="The list of gaps between the drums [mm]")
    resolution: Optional[int] = Field(None, description="The resolution of the recoater, the size of one pixel [µm]")

# API Endpoints

@router.get("/", response_model=ConfigurationResponse)
async def get_configuration(
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> ConfigurationResponse:
    """
    Get the current recoater configuration.
    
    Returns:
        ConfigurationResponse: Current configuration settings
    """
    try:
        logger.info("Getting recoater configuration")
        config_data = recoater_client.get_config()
        
        # Convert the response to our model format
        response = ConfigurationResponse(**config_data)
        
        logger.info("Successfully retrieved recoater configuration")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error while getting configuration: {e}")
        raise HTTPException(status_code=503, detail=f"Cannot connect to recoater: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error while getting configuration: {e}")
        raise HTTPException(status_code=502, detail=f"Recoater API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error while getting configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

@router.put("/")
async def set_configuration(
    config: ConfigurationRequest,
    recoater_client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the recoater configuration.
    
    Args:
        config: Configuration settings to apply
        
    Returns:
        Dict[str, Any]: Success response
    """
    try:
        logger.info(f"Setting recoater configuration: {config}")
        
        # Convert the request to a dictionary, excluding None values
        config_dict = config.model_dump(exclude_none=True)
        
        # Convert nested models to dictionaries
        if "build_space_dimensions" in config_dict and config_dict["build_space_dimensions"]:
            config_dict["build_space_dimensions"] = config_dict["build_space_dimensions"]
        
        response = recoater_client.set_config(config_dict)
        
        logger.info("Successfully set recoater configuration")
        return {
            "success": True,
            "message": "Configuration updated successfully",
            "config": config_dict
        }
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error while setting configuration: {e}")
        raise HTTPException(status_code=503, detail=f"Cannot connect to recoater: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error while setting configuration: {e}")
        raise HTTPException(status_code=502, detail=f"Recoater API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error while setting configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")
