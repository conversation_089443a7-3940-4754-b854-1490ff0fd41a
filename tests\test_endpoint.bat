@echo off
REM Test Recoater API Endpoints (Read-Only)
REM This script tests all read-only endpoints of the Recoater API
REM and keeps the window open to view results

set BASE_URL=http://*************:8080
set HEADERS=-H "Accept: application/json"

echo Testing Recoater API Endpoints (Read-Only)
echo =========================================
echo Base URL: %BASE_URL%
echo.

:test_endpoint
setlocal enabledelayedexpansion
set "test_number=1"

REM [1] Get API version
call :run_test "Get API Version" "/debug/version"

REM [2] Get recoater state
call :run_test "Get Recoater State" "/state"

REM [3] Get recoater configuration
call :run_test "Get Recoater Config" "/config"

REM [4] Get all drums info
call :run_test "Get All Drums Info" "/drums"

REM [5] Get specific drum info (drum 0)
call :run_test "Get Drum 0 Info" "/drums/0"

REM [6] Get drum configuration (drum 0)
call :run_test "Get Drum 0 Config" "/drums/0/config"

REM [7] Get drum ejection info (drum 0)
call :run_test "Get Drum 0 Ejection" "/drums/0/ejection"

REM [8] Get drum suction info (drum 0)
call :run_test "Get Drum 0 Suction" "/drums/0/suction"

REM [9] Get drum blade screws info (drum 0)
call :run_test "Get Drum 0 Blade Screws" "/drums/0/blade/screws"

REM [10] Get drum blade screw 0 info (drum 0)
call :run_test "Get Drum 0 Blade Screw 0" "/drums/0/blade/screws/0"

REM [11] Get drum blade screw 1 info (drum 0)
call :run_test "Get Drum 0 Blade Screw 1" "/drums/0/blade/screws/1"

REM [12] Get layer parameters
call :run_test "Get Layer Parameters" "/layer/parameters"

REM [13] Get leveler pressure
call :run_test "Get Leveler Pressure" "/leveler/pressure"

REM [14] Get leveler sensor state
call :run_test "Get Leveler Sensor" "/leveler/sensor"

REM [15] Get server logs (last 5 lines)
call :run_test "Get Server Logs" "/debug/logs?limit=5"

echo.
echo =========================================
echo API Testing Complete!
echo.
pause
goto :eof

:run_test
echo [%test_number%] %~1
echo Endpoint: %~2
echo -----------------------------------------
curl -v %HEADERS% "%BASE_URL%%~2"
echo.
echo -----------------------------------------
pause > nul
echo.
set /a test_number+=1
goto :eof
