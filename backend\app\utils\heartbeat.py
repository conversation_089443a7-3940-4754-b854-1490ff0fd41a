"""
Heartbeat Utility Module
========================

This module provides heartbeat functionality to maintain hardware connections
by periodically pinging the recoater hardware. The heartbeat helps ensure
the connection stays alive and can detect connection issues early.
"""

import asyncio
import logging
from typing import Optional

from app.dependencies import get_recoater_client_instance
from services.recoater_client import RecoaterConnectionError

logger = logging.getLogger(__name__)


class HeartbeatManager:
    """
    Manages the heartbeat lifecycle for maintaining recoater hardware connections.
    
    The heartbeat periodically calls the recoater's get_drums() endpoint to keep
    the connection alive and detect any connectivity issues.
    """
    
    def __init__(self, interval: float = 30.0):
        """
        Initialize the HeartbeatManager.
        
        Args:
            interval: Heartbeat interval in seconds (default: 30.0)
        """
        self.interval = interval
        self.task: Optional[asyncio.Task] = None
        self._running = False
    
    async def _heartbeat_loop(self) -> None:
        """
        Main heartbeat loop that periodically pings the recoater hardware.
        
        This loop runs continuously until cancelled, calling get_drums() every
        interval seconds. It handles connection errors gracefully and continues
        running even if individual heartbeat attempts fail.
        """
        logger.info(f"Starting heartbeat loop with {self.interval}s interval")
        
        while self._running:
            try:
                # Get the recoater client instance
                recoater_client = get_recoater_client_instance()
                
                if recoater_client is None:
                    logger.warning("Recoater client not available for heartbeat")
                else:
                    # Perform heartbeat by calling get_drums()
                    logger.debug("Performing heartbeat ping...")
                    await asyncio.get_event_loop().run_in_executor(
                        None, recoater_client.get_drums
                    )
                    logger.debug("Heartbeat ping successful")
                    
            except RecoaterConnectionError as e:
                logger.warning(f"Heartbeat connection error (will continue): {e}")
            except Exception as e:
                logger.error(f"Unexpected error in heartbeat loop (will continue): {e}")
            
            # Wait for the next heartbeat interval
            try:
                await asyncio.sleep(self.interval)
            except asyncio.CancelledError:
                logger.info("Heartbeat loop cancelled")
                break
        
        logger.info("Heartbeat loop stopped")
    
    def start(self) -> asyncio.Task:
        """
        Start the heartbeat task.
        
        Returns:
            asyncio.Task: The heartbeat task
            
        Raises:
            RuntimeError: If heartbeat is already running
        """
        if self._running:
            raise RuntimeError("Heartbeat is already running")
        
        self._running = True
        self.task = asyncio.create_task(self._heartbeat_loop())
        logger.info("Heartbeat task started")
        return self.task
    
    async def stop(self) -> None:
        """
        Stop the heartbeat task gracefully.
        
        This method cancels the heartbeat task and waits for it to complete.
        """
        if not self._running or self.task is None:
            logger.info("Heartbeat is not running")
            return
        
        logger.info("Stopping heartbeat task...")
        self._running = False
        
        # Cancel the task and wait for it to complete
        self.task.cancel()
        try:
            await self.task
        except asyncio.CancelledError:
            pass  # Expected when cancelling
        
        self.task = None
        logger.info("Heartbeat task stopped")
    
    @property
    def is_running(self) -> bool:
        """
        Check if the heartbeat is currently running.
        
        Returns:
            bool: True if heartbeat is running, False otherwise
        """
        return self._running and self.task is not None and not self.task.done()


# Global heartbeat manager instance
_heartbeat_manager: Optional[HeartbeatManager] = None


def start_heartbeat_task(interval: float = 30.0) -> asyncio.Task:
    """
    Start the heartbeat task with the specified interval.
    
    This function creates a global HeartbeatManager instance and starts
    the heartbeat task. It should be called during application startup.
    
    Args:
        interval: Heartbeat interval in seconds (default: 30.0)
        
    Returns:
        asyncio.Task: The heartbeat task
        
    Raises:
        RuntimeError: If heartbeat is already running
    """
    global _heartbeat_manager
    
    if _heartbeat_manager is not None and _heartbeat_manager.is_running:
        raise RuntimeError("Heartbeat task is already running")
    
    _heartbeat_manager = HeartbeatManager(interval)
    task = _heartbeat_manager.start()
    logger.info(f"Heartbeat task started with {interval}s interval")
    return task


async def stop_heartbeat_task() -> None:
    """
    Stop the heartbeat task gracefully.
    
    This function stops the global HeartbeatManager instance and should
    be called during application shutdown.
    """
    global _heartbeat_manager
    
    if _heartbeat_manager is None:
        logger.info("No heartbeat manager to stop")
        return
    
    await _heartbeat_manager.stop()
    _heartbeat_manager = None
    logger.info("Heartbeat task stopped")


def get_heartbeat_status() -> dict:
    """
    Get the current status of the heartbeat.
    
    Returns:
        dict: Dictionary containing heartbeat status information
    """
    global _heartbeat_manager
    
    if _heartbeat_manager is None:
        return {
            "running": False,
            "interval": None,
            "manager_exists": False
        }
    
    return {
        "running": _heartbeat_manager.is_running,
        "interval": _heartbeat_manager.interval,
        "manager_exists": True
    }