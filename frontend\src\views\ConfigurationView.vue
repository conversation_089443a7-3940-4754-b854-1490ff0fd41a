<template>
  <div class="configuration-view">
    <h2 class="view-title">Configuration</h2>

    <!-- Loading State -->
    <div v-if="loading" class="loading-state">
      <p>Loading configuration...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-state">
      <p class="error-message">{{ error }}</p>
      <button @click="loadConfiguration" class="retry-button">Retry</button>
    </div>

    <!-- Configuration Form -->
    <div v-else class="config-form">
      <!-- Build Space Configuration -->
      <div class="config-section">
        <h3 class="section-title">Build Space Configuration</h3>
        <div class="config-grid">
          <div class="config-field">
            <label for="diameter">Build Space Diameter (mm)</label>
            <input
              id="diameter"
              v-model.number="config.build_space_diameter"
              type="number"
              min="0"
              step="0.1"
              class="config-input"
              data-testid="diameter-input"
            />
          </div>
          <div class="config-field">
            <label for="length">Build Space Length (mm)</label>
            <input
              id="length"
              v-model.number="config.build_space_dimensions.length"
              type="number"
              min="0"
              step="0.1"
              class="config-input"
              data-testid="length-input"
            />
          </div>
          <div class="config-field">
            <label for="width">Build Space Width (mm)</label>
            <input
              id="width"
              v-model.number="config.build_space_dimensions.width"
              type="number"
              min="0"
              step="0.1"
              class="config-input"
              data-testid="width-input"
            />
          </div>
        </div>
      </div>

      <!-- System Configuration -->
      <div class="config-section">
        <h3 class="section-title">System Configuration</h3>
        <div class="config-grid">
          <div class="config-field">
            <label for="resolution">Resolution (µm)</label>
            <input
              id="resolution"
              v-model.number="config.resolution"
              type="number"
              min="0"
              step="1"
              class="config-input"
              data-testid="resolution-input"
            />
          </div>
          <div class="config-field">
            <label for="matrix-size">Ejection Matrix Size</label>
            <input
              id="matrix-size"
              v-model.number="config.ejection_matrix_size"
              type="number"
              min="0"
              step="1"
              class="config-input"
              data-testid="matrix-size-input"
            />
          </div>
        </div>
      </div>

      <!-- Drum Gap Configuration -->
      <div class="config-section">
        <h3 class="section-title">Drum Gap Configuration</h3>
        <div class="gaps-container">
          <div
            v-for="(gap, index) in config.gaps"
            :key="index"
            class="gap-field"
          >
            <label :for="`gap-${index}`">Gap {{ index + 1 }} (mm)</label>
            <input
              :id="`gap-${index}`"
              v-model.number="config.gaps[index]"
              type="number"
              min="0"
              step="0.1"
              class="config-input"
              :data-testid="`gap-${index}-input`"
            />
          </div>
          <div class="gap-controls">
            <button @click="addGap" class="add-gap-button" data-testid="add-gap-button">
              Add Gap
            </button>
            <button
              @click="removeGap"
              :disabled="config.gaps.length <= 1"
              class="remove-gap-button"
              data-testid="remove-gap-button"
            >
              Remove Gap
            </button>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button
          @click="saveConfiguration"
          :disabled="saving"
          class="save-button"
          data-testid="save-button"
        >
          {{ saving ? 'Saving...' : 'Save Configuration' }}
        </button>
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { apiService } from '../services/api.js'

export default {
  name: 'ConfigurationView',
  setup() {
    // Reactive state
    const loading = ref(false)
    const saving = ref(false)
    const error = ref(null)
    const successMessage = ref('')

    // Configuration data
    const config = ref({
      build_space_diameter: 250.0,
      build_space_dimensions: {
        length: 250.0,
        width: 96.0
      },
      ejection_matrix_size: 192,
      gaps: [130.0, 130.0],
      resolution: 500
    })

    // Store original config for reset functionality
    const originalConfig = ref({})

    // Methods
    const loadConfiguration = async () => {
      loading.value = true
      error.value = null

      try {
        const response = await apiService.getConfiguration()
        config.value = { ...response }
        originalConfig.value = { ...response }
      } catch (err) {
        console.error('Failed to load configuration:', err)
        error.value = 'Failed to load configuration. Please check your connection.'
      } finally {
        loading.value = false
      }
    }

    const saveConfiguration = async () => {
      saving.value = true
      error.value = null
      successMessage.value = ''

      try {
        await apiService.setConfiguration(config.value)
        successMessage.value = 'Configuration saved successfully!'
        originalConfig.value = { ...config.value }

        // Clear success message after 3 seconds
        setTimeout(() => {
          successMessage.value = ''
        }, 3000)
      } catch (err) {
        console.error('Failed to save configuration:', err)
        error.value = 'Failed to save configuration. Please try again.'
      } finally {
        saving.value = false
      }
    }

    const addGap = () => {
      config.value.gaps.push(130.0)
    }

    const removeGap = () => {
      if (config.value.gaps.length > 1) {
        config.value.gaps.pop()
      }
    }

    // Load configuration on component mount
    onMounted(() => {
      loadConfiguration()
    })

    return {
      loading,
      saving,
      error,
      successMessage,
      config,
      loadConfiguration,
      saveConfiguration,
      addGap,
      removeGap
    }
  }
}
</script>

<style scoped>
.configuration-view {
  max-width: 1000px;
  margin: 0 auto;
  padding: 1rem;
}

.view-title {
  margin: 0 0 2rem 0;
  color: #111827;
  font-size: 2rem;
  font-weight: 650;
}

/* Loading and Error States */
.loading-state, .error-state {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  text-align: center;
}

.error-message {
  color: #dc3545;
  margin-bottom: 1rem;
}

.retry-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.retry-button:hover {
  background: #0056b3;
}

/* Configuration Form */
.config-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.config-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.section-title {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
  border-bottom: 2px solid #e1e8ed;
  padding-bottom: 0.5rem;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.config-field {
  display: flex;
  flex-direction: column;
}

.config-field label {
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 500;
  font-size: 0.9rem;
}

.config-input {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.config-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Drum Gap Configuration */
.gaps-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.gap-field {
  display: flex;
  flex-direction: column;
}

.gap-field label {
  margin-bottom: 0.5rem;
  color: #495057;
  font-weight: 500;
  font-size: 0.9rem;
}

.gap-controls {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.add-gap-button, .remove-gap-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.15s ease-in-out;
}

.add-gap-button {
  background: #28a745;
  color: white;
}

.add-gap-button:hover {
  background: #218838;
}

.remove-gap-button {
  background: #dc3545;
  color: white;
}

.remove-gap-button:hover:not(:disabled) {
  background: #c82333;
}

.remove-gap-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
  margin-top: 1rem;
}

.save-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.15s ease-in-out;
  background: #007bff;
  color: white;
}

.save-button:hover:not(:disabled) {
  background: #0056b3;
}

.save-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.message {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
  max-width: 400px;
}

/* Success Message */
.success-message {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  background: #d4edda;
  color: #155724;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 100;
  max-width: 400px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .configuration-view {
    padding: 0.5rem;
  }

  .config-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .gap-controls {
    flex-direction: column;
  }
}
</style>
