<template>
  <div class="status-indicator">
    <div 
      class="status-dot" 
      :class="statusClass"
      :title="statusTooltip"
    ></div>
    <span class="status-text">{{ statusText }}</span>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStatusStore } from '../stores/status'

export default {
  name: 'StatusIndicator',
  setup() {
    const statusStore = useStatusStore()
    
    // Computed properties for status display
    const statusClass = computed(() => {
      if (!statusStore.isConnected) {
        return 'status-dot--disconnected'
      }
      return statusStore.isHealthy ? 'status-dot--connected' : 'status-dot--error'
    })
    
    const statusText = computed(() => {
      if (!statusStore.isConnected) {
        return 'Disconnected'
      }
      return statusStore.isHealthy ? 'Connected' : 'Error'
    })
    
    const statusTooltip = computed(() => {
      if (!statusStore.isConnected) {
        return 'Backend connection lost'
      }
      if (!statusStore.isHealthy) {
        return `Recoater error: ${statusStore.lastError || 'Unknown error'}`
      }
      return 'System operational'
    })
    
    onMounted(() => {
      // Initialize WebSocket connection when component mounts
      statusStore.connectWebSocket()
    })
    
    onUnmounted(() => {
      // Clean up WebSocket connection when component unmounts
      statusStore.disconnectWebSocket()
    })
    
    return {
      statusClass,
      statusText,
      statusTooltip
    }
  }
}
</script>

<style scoped>
.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.status-dot--connected {
  background-color: #27ae60;
  box-shadow: 0 0 8px rgba(39, 174, 96, 0.4);
}

.status-dot--disconnected {
  background-color: #e74c3c;
  box-shadow: 0 0 8px rgba(231, 76, 60, 0.4);
}

.status-dot--error {
  background-color: #f39c12;
  box-shadow: 0 0 8px rgba(243, 156, 18, 0.4);
}

.status-text {
  font-weight: 500;
  color: #ecf0f1;
}
</style>
