# Recoater HMI - User Guide

This guide explains how to use the Custom HMI to operate the Aerosint SPD Recoater.

## 1. Starting the System

### Prerequisites
- Python 3.8+ installed for backend
- Node.js 16+ installed for frontend
- Aerosint Recoater hardware (optional - see Development Mode below)

### Starting the Backend
1. Navigate to the `backend` directory
2. Install dependencies: `pip install -r requirements.txt`
3. Start the FastAPI server: `python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000`
4. Backend will be available at `http://localhost:8000`

### Starting the Frontend
1. Navigate to the `frontend` directory
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`
4. Frontend will be available at `http://localhost:5173`

### Accessing the HMI
1. Open a web browser (Chrome, Firefox, or Edge recommended)
2. Navigate to `http://localhost:5173`
3. The application will automatically connect to the backend and attempt to communicate with the recoater

## 1.1. Development Mode (No Hardware Required)

The HMI can run in development mode without requiring actual recoater hardware. This is useful for:
- Software development and testing
- Training and demonstration
- UI development without hardware access

### Enabling Development Mode
1. Navigate to the `backend` directory
2. Open the `.env` file
3. Set `DEVELOPMENT_MODE=true`
4. Start the backend as normal

### Development Mode Features
- **Mock Data**: Realistic simulated recoater data with dynamic updates
- **Full Functionality**: All UI features work with simulated responses
- **Real-time Updates**: WebSocket updates with simulated axis positions and status changes
- **Error Simulation**: Can test error handling without hardware failures
- **Temperature Simulation**: Dynamic temperature readings with realistic fluctuations
- **Progress Simulation**: Simulated job progress for testing print workflows

### Switching to Hardware Mode
1. Ensure Aerosint Recoater hardware is powered on and connected (IP: *************)
2. Set `DEVELOPMENT_MODE=false` in the `.env` file
3. Restart the backend
4. The system will now communicate with actual hardware



## 2. The Main Interface

**Note**: All features described below work in both hardware mode and development mode. In development mode, you'll see simulated data and responses instead of actual hardware values.

The interface is divided into several main tabs, accessible from a navigation menu on the left:

-   **Status:** The main landing page showing the overall system connection status and detailed information.

-   **Axis:** Manual controls for the X and Z axes (Phase 2 - Completed).

-   **Recoater:** Controls for the individual drums, hoppers, and the leveler (Phases 3-5 - Completed).

-   **Print:** For loading print files and managing the printing process (Phases 6-7 - Planned).

-   **Configuration:** For setting system-level parameters like the build area (Phase 9 - Completed).

### 2.1. Status Indicator

In the top-right corner of the header, you'll see a real-time status indicator:

- **Green dot + "Connected"**: HMI is successfully communicating with both the backend and recoater hardware
- **Red dot + "Disconnected"**: Connection to the backend has been lost
- **Orange dot + "Error"**: Connected to backend but the recoater hardware has issues

The status indicator updates automatically in real-time via WebSocket connection.

### 2.2. Status Page

The main Status page provides detailed information about the system:

**Connection Status Card:**
- Backend connection status
- Recoater hardware connection status
- Last update timestamp

**System Information Card:**
- Current recoater state (ready, printing, error, etc.)
- Additional system details when available

**Error Information Card:**
- Displays when there are connection or hardware errors
- Shows detailed error messages for troubleshooting

**Manual Refresh:**
- Use the "Refresh Status" button to manually update the status
- Automatic updates continue via WebSocket in the background



### 2.2. Using the "Axis" Window

This window allows you to manually move the machine's X and Z axes and control the punch gripper.

#### Connection Status
- The status card at the top shows the current connection state
- Green dot: Connected and operational
- Red dot: Disconnected from backend or hardware

#### Movement Parameters
1. **Distance (mm)**: Set the distance for each movement (minimum 0 mm)
2. **Speed (mm/s)**: Set the movement speed (minimum 0.1 mm/s)

#### Axis Controls

**X Axis (Horizontal Movement):**
- **Position Display**: Shows current X position in mm with 2 decimal precision
- **Status**: Indicates if axis is "Moving" or "Stopped"
- **Home Button** (`🏠`): Returns X axis to reference position
- **Left Arrow** (`←`): Moves X axis left by specified distance
- **Right Arrow** (`→`): Moves X axis right by specified distance

**Z Axis (Vertical Movement):**
- **Position Display**: Shows current Z position in mm with 2 decimal precision
- **Status**: Indicates if axis is "Moving" or "Stopped"
- **Home Button** (`🏠`): Returns Z axis to reference position
- **Up Arrow** (`↑`): Moves Z axis up by specified distance
- **Down Arrow** (`↓`): Moves Z axis down by specified distance

#### Gripper Control
- **Status Display**: Shows current gripper state (Activated/Deactivated)
- **Toggle Switch**: Click to activate or deactivate the punch gripper
- The switch position reflects the current gripper state

#### Safety Features
- All controls are automatically disabled when disconnected from hardware
- Movement buttons are disabled when the respective axis is already moving
- Homing operations take priority and disable other movements
- Error messages are displayed for failed operations

#### Recommended Workflow
1. **Check Connection**: Ensure the status shows "Connected"
2. **Home Axes**: Press the home button (`🏠`) for both X and Z axes before starting operations
3. **Set Parameters**: Enter desired distance and speed values
4. **Move Axes**: Use directional arrows to position the axes as needed
5. **Control Gripper**: Activate gripper when needed for part handling

#### Real-time Updates
- Axis positions update automatically every second via WebSocket connection
- Movement status changes are reflected immediately in the interface
- Connection status updates in real-time

### 2.3. Using the "Recoater" Window

This window provides comprehensive control over the recoater drums and hopper systems. The interface is divided into two main sections: Drum Controls and Hopper Controls.

#### Connection Status
- The status indicator at the top shows the current connection state
- Green dot: Connected and operational
- Red dot: Disconnected from backend or hardware
- All controls are automatically disabled when disconnected

#### Drum Controls

Each drum has its own control card with the following features:

**Motion Control:**
- **Speed (mm/s)**: Set the rotation speed (minimum 10 mm/s)
- **Distance (mm)**: Set the distance for relative movements
- **Turns**: Set the number of rotations for turn-based movements
- **Mode Selection**: Choose between Relative, Absolute, Turns, Speed, or Homing modes
- **Start Motion Button**: Initiates the selected motion command
- **Cancel Motion Button**: Stops any active motion (enabled only when drum is moving)

**Pressure Control:**
- **Ejection Pressure**: Set and monitor ejection pressure in Pascals
- **Suction Pressure**: Set and monitor suction pressure in Pascals
- Real-time pressure readings with target vs. actual values

**Status Display:**
- Current drum position and rotation status
- Motion state (Moving/Stopped)
- Pressure readings with units

#### Hopper Controls (Scraping Blade)

Each drum has an associated hopper control card for managing the scraping blade:

**Collective Blade Motion:**
- **Motion Mode**: Select between Relative, Absolute, or Homing modes
- **Distance (µm)**: Set the distance in micrometers (for non-homing modes)
- **Start Motion**: Initiates collective motion for both screws
- **Cancel Motion**: Stops any active collective motion

**Individual Screw Control:**
- **Screw 0 & Screw 1**: Independent control for each screw
- **Distance (µm)**: Set relative distance for individual screw movement
- **Move Button**: Start motion for the specific screw
- **Stop Button**: Cancel motion for the specific screw

**Status Display:**
- Real-time position readings for each screw in micrometers
- Running status indicators (green = running, gray = stopped)
- Overall blade status (Running/Stopped)

#### Leveler Control

The leveler control section provides pressure management and sensor monitoring for the leveling system:

**Pressure Control:**
- **Current Pressure**: Real-time display of actual leveler pressure in Pascals (Pa)
- **Target Pressure**: Shows the currently set target pressure
- **Maximum Pressure**: Displays the system's maximum allowable pressure
- **Target Input**: Enter desired pressure value (must be positive and within limits)
- **Set Pressure Button**: Apply the new target pressure setting

**Magnetic Sensor Status:**
- **Sensor State**: Visual indicator showing Active (green) or Inactive (gray)
- **Status Description**: Text description of sensor state
- **Real-time Updates**: Sensor status updates automatically via WebSocket

**Status Display:**
- Pressure values displayed with 2 decimal precision in Pascals
- Visual connection indicator (green dot = connected, red dot = disconnected)
- Auto-clearing error messages for failed operations
- Loading indicators during pressure setting operations

#### Safety Features
- All controls are disabled when not connected to hardware
- Motion buttons are disabled when the respective component is already moving
- Cancel/Stop buttons are only enabled when motion is active
- Error messages are displayed for failed operations
- Real-time status updates prevent conflicting commands

#### Recommended Workflow
1. **Check Connection**: Ensure the status shows "Connected"
2. **Home Operations**: Use homing mode for both drums and blades on startup
3. **Set Parameters**: Configure speeds, distances, and pressures as needed
4. **Execute Motions**: Use motion controls for positioning
5. **Monitor Status**: Watch real-time updates for completion and errors

#### Real-time Updates
- All drum and blade positions update automatically via WebSocket
- Motion status changes are reflected immediately
- Pressure readings update continuously
- Connection status updates in real-time

### 2.4. System Configuration ("Configuration" Window)

The Configuration window allows you to set system-level parameters for the recoater hardware. This is where you configure the fundamental settings that define your build space and system capabilities.

#### Build Space Configuration

**Build Space Diameter (mm)**
- Set the diameter of the circular build space
- Must be a positive value
- Defines the maximum printable area diameter

**Build Space Dimensions**
- **Length (mm)**: The length of the rectangular build space
- **Width (mm)**: The width of the rectangular build space
- Both values must be positive
- Used for rectangular build configurations

#### System Configuration

**Resolution (µm)**
- Set the resolution of the recoater system
- Measured in micrometers (µm)
- Defines the size of one pixel in the system
- Must be a positive integer value

**Ejection Matrix Size**
- Set the number of points in the ejection matrix
- Must be a positive integer
- Defines the granularity of the ejection control system

#### Drum Gap Configuration

**Gap Management**
- Configure the gaps between drums in millimeters
- Each gap represents the spacing between consecutive drums
- **Add Gap**: Click to add a new gap configuration
- **Remove Gap**: Click to remove the last gap (minimum one gap required)
- All gap values must be positive

#### Configuration Management

**Save Configuration**
- Click "Save Configuration" to apply your settings to the hardware
- The button shows "Saving..." while the operation is in progress
- A success message appears when configuration is saved successfully
- The button is disabled during save operations

**Reset to Defaults**
- Click "Reset to Defaults" to restore the original configuration values
- This reverts all changes back to the last saved configuration
- No confirmation dialog - changes are immediate

#### Loading and Error Handling

**Loading State**
- The interface shows "Loading configuration..." while fetching current settings
- All controls are hidden during loading

**Error Handling**
- Connection errors display with a "Retry" button
- Save errors show user-friendly error messages
- Form validation prevents invalid values from being submitted

**Success Feedback**
- Success messages appear after successful save operations
- Messages automatically disappear after 3 seconds
- Green styling indicates successful operations

#### Usage Tips

1. **Initial Setup**: Configure build space parameters before starting any print jobs
2. **Gap Configuration**: Ensure gap values match your physical drum spacing
3. **Resolution Setting**: Higher resolution values provide finer control but may impact performance
4. **Save Regularly**: Save configuration changes before switching to other windows
5. **Validation**: The form prevents invalid inputs (negative values, missing required fields)

### 2.5. Running a Print Job ("Print" Window)

This is the main window for executing a print.

1.  **Load Geometry:** For each drum you intend to use, click the "Upload" button (`⬆️`) and select the appropriate `.CLI` or `.PNG` file for that material.

2.  **Set Parameters:** On the right-hand panel, configure all print parameters, such as `Patterning speed`, `Filling drum`, and `Layer thickness`.

3.  **Preview:** The central area will show a preview of the deposited powder based on your loaded files.

4.  **CLI Layer Management:**
    *   **Upload Multi-layer CLI:** Use the dedicated section to upload a multi-layer `.cli` file. The system will parse it and display the total number of layers.
    *   **Preview Individual Layers:** Select a layer number and click "Preview Layer" to view a PNG representation of that specific layer.
    *   **Send Layer to Drum:** After previewing, you can select a drum and click "Send Layer to Recoater" to upload the CLI data for that single layer to the chosen drum.

5.  **Start Printing:** Once everything is configured, press the **"Start Printing"** button.

6.  **Stop Printing:** To cancel the active job, press the **"Stop Printing"** button.

