# Architecture Design: Multi-Material Print Job System

## System Architecture Overview

```mermaid
graph TB
    subgraph "Operator Interface"
        UI[Vue.js Frontend<br/>Multi-Material Touch Interface]
    end
    
    subgraph "Coordination Layer"
        API[FastAPI Backend<br/>Multi-Material Logic]
        OPC[OPC UA Client<br/>Real-time Communication]
    end
    
    subgraph "Hardware Control Layer"
        PLC[TwinCAT PLC<br/>OPC UA Server<br/>3-Drum Coordinator]
        MCP[MCP Server<br/>Galvo Control]
    end
    
    subgraph "Multi-Material Recoater Hardware"
        AER1[Aerosint Drum 0<br/>Material 1]
        AER2[Aerosint Drum 1<br/>Material 2]
        AER3[Aerosint Drum 2<br/>Material 3]
        AER_SRV[Aerosint Server<br/>3-Drum Management]
        HW[Physical Recoater<br/>3-Drum System]
    end
    
    UI <==> API
    API <==> OPC
    OPC <==> PLC
    PLC <==> MCP
    API <==> AER_SRV
    AER_SRV <==> AER1
    AER_SRV <==> AER2
    AER_SRV <==> AER3
    PLC <==> HW
    AER1 <==> HW
    AER2 <==> HW
    AER3 <==> HW
```

## Component Responsibilities

### FastAPI Backend (Enhanced for Multi-Material)
**Primary Responsibilities:**
- Multi-material job state management (3 drums)
- CLI file parsing and validation for 3 files
- Layer sequencing across all drums
- Aerosint server communication for all drums
- OPC UA client operations
- Multi-drum error handling and recovery

**New Multi-Material Capabilities:**
- 3-file CLI upload and validation
- Layer-by-layer CLI file processing per drum
- Real-time coordination with PLC for 3-drum operation
- Multi-material job progress tracking
- Per-drum system error management
- Drum synchronization logic

### TwinCAT PLC (OPC UA Server - Multi-Drum)
**Primary Responsibilities:**
- Hardware timing coordination
- Physical safety controls
- MCP server integration
- Real-time state management

**Key Functions:**
- Monitor recoater ready signals
- Trigger physical movement sequences
- Coordinate galvo scanning operations
- Provide emergency stop functionality

### Aerosint Server (Unchanged)
**Existing Capabilities:**
- Single-layer print job management
- Hardware status reporting ("ready"/"printing"/"error")
- Geometry file upload and processing
- Physical recoater control

## Data Flow Architecture

### Multi-Layer Job Initialization
```mermaid
sequenceDiagram
    participant Op as Operator
    participant UI as Vue.js Frontend
    participant API as FastAPI Backend
    participant OPC as OPC UA Client
    participant PLC as TwinCAT PLC
    participant AER as Aerosint Server
    
    Op->>UI: Upload CLI file & Start Job
    UI->>API: POST /cli/{file_id}/start-multilayer-job
    API->>API: Parse CLI, Initialize Job State
    API->>OPC: Connect to PLC
    API->>PLC: Set job_active=TRUE, total_layers=N
    API->>AER: Upload Layer 1, Start Print Job
    API->>PLC: Monitor recoater_ready_to_print
    API->>UI: Return Job Started Response
```

### Layer Processing Coordination
```mermaid
sequenceDiagram
    participant API as FastAPI Backend
    participant PLC as TwinCAT PLC
    participant AER as Aerosint Server
    participant MCP as MCP Server
    
    loop For Each Layer
        API->>AER: Monitor Print Status
        AER-->>API: Status: "printing"
        API->>PLC: Set recoater_ready_to_print=TRUE
        PLC->>PLC: Start Physical Movement
        PLC->>PLC: Set recoater_start_signal=TRUE
        PLC->>PLC: Monitor Movement Completion
        AER-->>API: Status: "ready" (Layer Complete)
        API->>PLC: Set recoater_layer_complete=TRUE
        PLC->>MCP: Set galvo_ready_to_scan=TRUE
        MCP->>MCP: Execute Galvo Scan
        MCP->>PLC: Set galvo_scan_complete=TRUE
        PLC-->>API: galvo_scan_complete=TRUE (Event)
        API->>API: Increment Layer, Send Next Layer
    end
```

## OPC UA Variable Architecture

### Shared State Variables

The TwinCAT PLC exposes the following OPC UA variables for coordination:

```typescript
// Job Control Variables
interface JobControlVariables {
  job_active: boolean;              // Backend sets TRUE at start, FALSE at end
  total_layers: number;             // Backend sets once at job start  
  current_layer: number;            // Backend manages, PLC reads
}

// Recoater Coordination Variables
interface RecoaterCoordination {
  recoater_ready_to_print: boolean; // Backend writes when Aerosint ready
  recoater_start_signal: boolean;   // PLC writes to trigger movement
  recoater_layer_complete: boolean; // Backend writes when deposition done
}

// Galvo Coordination Variables  
interface GalvoCoordination {
  galvo_ready_to_scan: boolean;     // PLC writes when layer ready
  galvo_scan_complete: boolean;     // MCP/PLC writes when scan complete
}

// System Status Variables
interface SystemStatus {
  system_error: boolean;            // Any component can write
  error_message: string;            // Description of error
}
```

### Variable Ownership and Authority

| Variable | Write Authority | Read Access | Purpose |
|----------|----------------|-------------|---------|
| `job_active` | Backend | All | Overall job state control |
| `total_layers` | Backend | All | Job scope information |
| `current_layer` | Backend | All | Progress tracking |
| `recoater_ready_to_print` | Backend | All | Signal recoater readiness |
| `recoater_start_signal` | PLC | All | Trigger physical movement |
| `recoater_layer_complete` | Backend | All | Layer completion detection |
| `galvo_ready_to_scan` | PLC | All | Galvo operation trigger |
| `galvo_scan_complete` | PLC/MCP | All | Galvo completion signal |
| `system_error` | Any | All | Error state indication |
| `error_message` | Any | All | Error details |

## State Machine Design

### Multi-Layer Job State Machine

```mermaid
stateDiagram-v2
    [*] --> Idle: System Ready
    
    Idle --> JobInit: Start Multi-Layer Job
    JobInit --> WaitingForPrint: Layer Uploaded
    
    WaitingForPrint --> RecoaterActive: Aerosint Status = "printing"
    RecoaterActive --> WaitingForDeposition: PLC Triggered
    WaitingForDeposition --> WaitingForGalvo: Aerosint Status = "ready"
    WaitingForGalvo --> LayerComplete: galvo_scan_complete = TRUE
    
    LayerComplete --> WaitingForPrint: More Layers Available
    LayerComplete --> JobComplete: All Layers Processed
    
    JobComplete --> Idle: Job Finished
    
    WaitingForPrint --> Error: Timeout/Error
    RecoaterActive --> Error: Timeout/Error  
    WaitingForDeposition --> Error: Timeout/Error
    WaitingForGalvo --> Error: Timeout/Error
    
    Error --> Idle: Error Cleared
    Error --> [*]: System Reset
```

### State Transitions and Triggers

| Current State | Trigger | Next State | Actions |
|---------------|---------|------------|---------|
| Idle | Start Job Request | JobInit | Initialize job state, parse CLI |
| JobInit | Layer Upload Complete | WaitingForPrint | Send layer to Aerosint |
| WaitingForPrint | Aerosint = "printing" | RecoaterActive | Set recoater_ready_to_print=TRUE |
| RecoaterActive | PLC Movement Started | WaitingForDeposition | Monitor for completion |
| WaitingForDeposition | Aerosint = "ready" | WaitingForGalvo | Set recoater_layer_complete=TRUE |
| WaitingForGalvo | galvo_scan_complete | LayerComplete | Increment layer counter |
| LayerComplete | More layers exist | WaitingForPrint | Send next layer |
| LayerComplete | No more layers | JobComplete | Finalize job |
| Any State | Error/Timeout | Error | Pause all operations |

## Communication Protocols

### OPC UA Client Configuration

```python
class OPCUAConfig:
    ENDPOINT = "opc.tcp://localhost:4840"
    NAMESPACE = 2
    CONNECTION_TIMEOUT = 5.0
    SUBSCRIPTION_INTERVAL = 100  # milliseconds
    
    # Node addressing
    VARIABLE_NODES = {
        "job_active": "ns=2;s=job_active",
        "total_layers": "ns=2;s=total_layers",
        "current_layer": "ns=2;s=current_layer",
        # ... additional nodes
    }
```

### Aerosint API Integration

```python
class AerosintIntegration:
    BASE_URL = "http://*************:8080"
    
    # Key endpoints for multi-layer operation
    ENDPOINTS = {
        "upload_geometry": "PUT /drums/{drum_id}/geometry",
        "start_print": "POST /print/job", 
        "get_status": "GET /state",
        "cancel_print": "DELETE /print/job"
    }
    
    # Expected status values
    STATUS_READY = "ready"
    STATUS_PRINTING = "printing" 
    STATUS_ERROR = "error"
```

### Event-Driven Communication

**OPC UA Subscriptions:**
- Subscribe to `galvo_scan_complete` for automatic layer progression
- Subscribe to `system_error` for immediate error handling
- Real-time updates without polling overhead

**HTTP Polling for Aerosint:**
- Poll Aerosint status every 1 second during active operations
- Detect transitions from "printing" to "ready"
- Handle connection errors and timeouts

## Error Handling Architecture

### Error Categories and Responses

| Error Category | Detection Method | Response Strategy | AI Implementation Notes |
|----------------|------------------|-------------------|------------------------|
| **Communication Errors** | Connection timeouts, network failures | Retry with exponential backoff | Use existing patterns from `recoater_client.py` |
| **Hardware Errors** | Aerosint error state, PLC fault signals | Immediate stop, operator notification | Implement circuit breaker pattern |
| **Coordination Errors** | State synchronization failures | Pause job, diagnostic logging | Add correlation IDs for multi-component tracing |
| **User Errors** | Invalid CLI files, configuration issues | Reject operation, clear error message | Use FastAPI HTTPException with detailed messages |

### AI Agent Error Handling Patterns

**Recommended Error Handling Implementation:**

```python
# Use existing error classes from recoater_client.py
from services.recoater_client import RecoaterConnectionError, RecoaterAPIError

# Add new error classes for multi-material operations
class MultiMaterialJobError(Exception):
    """Base exception for multi-material job operations"""
    pass

class DrumSynchronizationError(MultiMaterialJobError):
    """Raised when drums fail to synchronize"""
    pass

class OPCUAConnectionError(Exception):
    """Raised when OPC UA connection fails"""
    pass

# Implement consistent error handling patterns
async def handle_coordination_error(error: Exception, job_id: str):
    """Standard error handling for coordination failures"""
    logger.error(f"Coordination error in job {job_id}: {error}")
    # Pause job, notify operator, log diagnostics
    await pause_job_safely(job_id)
    await notify_operator(error)
```

### Error Recovery Mechanisms

**Automatic Recovery:**
- Network reconnection with retry logic
- Temporary communication failures
- Transient hardware faults

**Manual Recovery:**
- Hardware failures requiring intervention
- Configuration errors
- Safety-related stops

**Recovery Process:**
1. **Detection**: Error identified by monitoring systems
2. **Isolation**: Stop current operations safely
3. **Notification**: Alert operator with clear error message
4. **Diagnosis**: Provide diagnostic information
5. **Recovery**: Allow manual error clearing or automatic retry

## Performance Considerations

### Communication Optimization

**OPC UA Optimization:**
- Use subscriptions instead of polling for real-time events
- Batch variable writes when possible
- Maintain persistent connections

**Aerosint Communication:**
- Efficient HTTP connection pooling
- Appropriate polling intervals (1-second for active operations)
- Timeout handling to prevent blocking

### Resource Management

**Memory Management:**
- CLI file caching with automatic cleanup
- Limit concurrent job processing
- Efficient data structures for layer management

**CPU Optimization:**
- Asynchronous processing for I/O operations
- Non-blocking coordination logic
- Efficient event handling

## Security Considerations

### Network Security
- OPC UA security policies and certificates
- HTTPS for Aerosint communication when available
- Network segmentation for industrial components

### Access Control
- Authentication for multi-layer job operations
- Role-based access for error clearing
- Audit logging for all operations

### Data Protection
- Secure storage of CLI files and job data
- Protection of coordination state information
- Safe handling of error messages and logs

## Scalability and Extensibility

### Future Enhancement Points

**Additional Recoater Support:**
- Multi-recoater coordination
- Load balancing across hardware
- Parallel processing capabilities

**Advanced Coordination:**
- Additional MCP server types
- Complex timing requirements
- Multi-step layer processing

**Monitoring and Analytics:**
- Performance metrics collection
- Predictive maintenance integration
- Advanced error analytics

## Integration Testing Strategy

### Component Integration Tests
1. **OPC UA Communication**: Verify bidirectional variable access
2. **Aerosint Integration**: Test all API operations and error conditions
3. **State Coordination**: Validate state machine transitions
4. **Error Handling**: Test all error scenarios and recovery

### End-to-End Testing
1. **Complete Multi-Layer Jobs**: Test full automation workflow
2. **Error Recovery**: Validate error handling and recovery procedures
3. **Performance Testing**: Verify timing and resource requirements
4. **Safety Testing**: Validate emergency stop and safety functions

This architecture provides a robust foundation for implementing automated multi-layer printing while maintaining system reliability and operator safety.
