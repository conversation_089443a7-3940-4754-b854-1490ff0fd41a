# Architecture Design: Multi-Material Print Job System

## System Architecture Overview

```mermaid
graph TB
    subgraph "Operator Interface"
        UI[Vue.js Frontend<br/>Multi-Material Touch Interface]
    end
    
    subgraph "Coordination Layer"
        API[FastAPI Backend<br/>Multi-Material Logic<br/>OPC UA Server]
    end
    
    subgraph "Hardware Control Layer"
        PLC[TwinCAT PLC<br/>OPC UA Client<br/>Physical Control]
    end
    
    subgraph "Multi-Material Recoater Hardware"
        AER1[Aerosint Drum 0<br/>Material 1]
        AER2[Aerosint Drum 1<br/>Material 2]
        AER3[Aerosint Drum 2<br/>Material 3]
        AER_SRV[Aerosint Server<br/>3-Drum Management]
        HW[Physical Recoater<br/>3-Drum System]
    end
    
    UI <==> API
    API <==> PLC
    API <==> AER_SRV
    AER_SRV <==> AER1
    AER_SRV <==> AER2
    AER_SRV <==> AER3
    PLC <==> HW
    AER1 <==> HW
    AER2 <==> HW
    AER3 <==> HW
```

## Component Responsibilities

### FastAPI Backend (Enhanced for Multi-Material)
**Primary Responsibilities:**
- Multi-material job state management (3 drums)
- CLI file parsing and validation for 3 files
- Layer sequencing across all drums
- Aerosint server communication for all drums
- **OPC UA server operations** (hosting variables for PLC)
- Multi-drum error handling and recovery

**New Multi-Material Capabilities:**
- 3-file CLI upload and validation
- Layer-by-layer CLI file processing per drum
- **OPC UA server hosting coordination variables**
- Multi-material job progress tracking
- Simplified coordination with minimal PLC dependency
- Independent layer progression based on Aerosint status

### TwinCAT PLC (OPC UA Client - Simplified)
**Primary Responsibilities:**
- Physical recoater movement control
- Monitor coordination variables from backend
- Error reporting to backend
- Emergency stop functionality

**Key Functions:**
- Read `recoater_ready_to_print` from backend OPC UA server
- Control physical recoater movement when signal is TRUE
- Set `plc_error` flag if hardware issues occur
- Provide emergency stop functionality

### Aerosint Server (Unchanged)
**Existing Capabilities:**
- Single-layer print job management
- Hardware status reporting ("ready"/"printing"/"error")
- Geometry file upload and processing
- Physical recoater control

## Data Flow Architecture

### Detailed Multi-Material Workflow

**OPC UA Variables (Initialization/Inactive State):**
```
# Job Control
job_active: bool (FALSE)                    # Backend sets TRUE at start, FALSE at end
total_layers: int (0)                       # Backend sets once at job start
current_layer: int (0)                      # Backend manages, PLC reads

# Recoater Coordination
recoater_ready_to_print: bool (FALSE)       # Backend writes when Aerosint is ready
recoater_start_signal: bool (FALSE)         # PLC writes to trigger recoater movement
recoater_layer_complete: bool (FALSE)       # Backend/PLC writes when deposition complete

# MCP/Galvo Coordination
galvo_ready_to_scan: bool (FALSE)           # PLC writes when layer ready for scanning
galvo_scan_complete: bool (FALSE)           # MCP/PLC writes when scan complete

# System Status
system_error: bool (FALSE)                  # Any component can write
error_message: string ("")                  # Description of error
```

**Workflow Steps:**

**1. JOB INITIALIZATION (Backend)**
- Upload 3 CLI files with potentially different layer counts to FastAPI backend
- Parse and store 3 CLI files in memory
- Calculate total_layers = max(layers_drum0, layers_drum1, layers_drum2)
- Operator clicks 'Start Print Job'
- Set: job_active=TRUE, total_layers=max_layers, current_layer=1
- Grey out whole print frontend except for cancel print button
- Upload layer 1 for all drums to Aerosint (or empty CLI for drums without layer 1)
- Start print job on Aerosint

**2. LAYER PROCESSING LOOP**
- Backend: Wait for Aerosint status = 'printing'
- Backend: If 'printing', set recoater_ready_to_print=TRUE
- PLC: Monitor recoater_ready_to_print
- PLC: If TRUE, start physical recoater deposition
- Backend: Wait for Aerosint status = 'ready' (indicates deposition complete)
- Backend: Set recoater_layer_complete=TRUE
- Backend: Increment current_layer
- Backend: Reset coordination flags to FALSE
- Backend: Upload cli[current_layer] to all 3 drums (or empty CLI for depleted drums)
- Backend: Start print job on Aerosint (if layers remaining)
- Repeat until current_layer > total_layers

**3. JOB COMPLETION**
- Backend: Set job_active=FALSE
- All systems return to idle state

**4. ERROR RECOVERY (Operator Actions)**
- **View Error**: Operator sees persistent error modal with details
- **Resolve Issue**: Operator addresses the underlying problem (hardware, network, etc.)
- **Clear Error Flags**: Operator clicks "Clear Error Flags" button
- **Resume Operations**: System can resume normal operations after error flags cleared

### Depleted Drum Handling Strategy

**Problem**: When drums have different layer counts, some drums will run out of layers before the job completes.

**Solution Options**:
1. **Empty CLI Upload**: Upload empty CLI files to depleted drums
   - Pros: Ensures drum has "something" to process, prevents memory retention
   - Cons: May cause hardware errors if empty CLI is invalid

2. **Skip Upload**: Don't upload anything to depleted drums
   - Pros: Simpler logic, no risk of invalid CLI
   - Cons: Risk that drum retains last layer in memory and reprints it

**Recommended Implementation**:
- **Phase 1**: Start with empty CLI upload approach
- **Phase 2**: If hardware issues occur, switch to skip upload with explicit drum disable

**Empty CLI Structure**:
```
$$HEADERSTART
$$HEADEREND
$$GEOMETRYSTART
$$LAYER/0.0
$$HEADEREND
```

**Implementation Logic**:
```python
def get_layer_for_drum(drum_id: int, layer_num: int) -> str:
    if layer_num <= len(drum_layers[drum_id]):
        return drum_layers[drum_id][layer_num - 1]
    else:
        # Drum depleted - return empty CLI
        return generate_empty_cli()
```

### Multi-Layer Job Initialization
```mermaid
sequenceDiagram
    participant Op as Operator
    participant UI as Vue.js Frontend
    participant API as FastAPI Backend
    participant OPC as OPC UA Client
    participant PLC as TwinCAT PLC
    participant AER as Aerosint Server
    
    Op->>UI: Upload 3 CLI files & Start Job
    UI->>API: POST /cli/start-multimaterial-job
    API->>API: Parse 3 CLI files, Calculate total_layers=max(layers)
    API->>API: Initialize Multi-Material Job State
    API->>API: Start OPC UA Server
    API->>API: Set job_active=TRUE, total_layers=N, current_layer=1
    API->>AER: Upload Layer 1 to all 3 drums, Start Print Job
    API->>UI: Return Job Started Response
    UI->>UI: Grey out print interface except Cancel button
```

### Layer Processing Coordination
```mermaid
sequenceDiagram
    participant API as FastAPI Backend<br/>(OPC UA Server)
    participant PLC as TwinCAT PLC<br/>(OPC UA Client)
    participant AER as Aerosint Server

    loop For Each Layer
        API->>AER: Monitor Print Status
        AER-->>API: Status: "printing"
        API->>API: Set recoater_ready_to_print=TRUE (OPC UA)
        PLC->>API: Read recoater_ready_to_print (OPC UA)
        PLC->>PLC: Start Physical Recoater Movement
        API->>AER: Monitor Print Status
        AER-->>API: Status: "ready" (Layer Complete)
        API->>API: Set recoater_layer_complete=TRUE (OPC UA)
        API->>API: Increment current_layer
        API->>API: Reset coordination flags to FALSE
        API->>AER: Upload next layer to all 3 drums
        API->>AER: Start print job (if layers remaining)
    end
```

## OPC UA Variable Architecture

### Shared State Variables

The TwinCAT PLC exposes the following OPC UA variables for coordination:

```typescript
// Job Control Variables (Initialization: job_active=FALSE, others=0)
interface JobControlVariables {
  job_active: boolean;              // Backend sets TRUE at start, FALSE at end
  total_layers: number;             // Backend sets once at job start
  current_layer: number;            // Backend manages, PLC reads
}

// Recoater Coordination Variables (Initialization: all FALSE)
interface RecoaterCoordination {
  recoater_ready_to_print: boolean; // Backend writes when Aerosint ready
  recoater_layer_complete: boolean; // Backend writes when deposition done
}

// System Status Variables (Initialization: all FALSE)
interface SystemStatus {
  backend_error: boolean;           // Backend writes if any issue arises
  plc_error: boolean;               // PLC writes if any issues
}

// Note: When backend_error=TRUE or plc_error=TRUE, pause all operations and display error to operator
```

### Variable Ownership and Authority

| Variable | Write Authority | Read Access | Purpose |
|----------|----------------|-------------|---------|
| `job_active` | Backend | All | Overall job state control |
| `total_layers` | Backend | All | Job scope information |
| `current_layer` | Backend | All | Progress tracking |
| `recoater_ready_to_print` | Backend | All | Signal recoater readiness |
| `recoater_layer_complete` | Backend | All | Layer completion detection |
| `backend_error` | Backend | All | Backend error state indication |
| `plc_error` | PLC | All | PLC error state indication |

## State Machine Design

### Multi-Layer Job State Machine

```mermaid
stateDiagram-v2
    [*] --> Idle: System Ready
    
    Idle --> JobInit: Start Multi-Layer Job
    JobInit --> WaitingForPrint: Layer Uploaded
    
    WaitingForPrint --> RecoaterActive: Aerosint Status = "printing"
    RecoaterActive --> WaitingForDeposition: PLC Triggered
    WaitingForDeposition --> WaitingForGalvo: Aerosint Status = "ready"
    WaitingForGalvo --> LayerComplete: galvo_scan_complete = TRUE
    
    LayerComplete --> WaitingForPrint: More Layers Available
    LayerComplete --> JobComplete: All Layers Processed
    
    JobComplete --> Idle: Job Finished
    
    WaitingForPrint --> Error: Timeout/Error
    RecoaterActive --> Error: Timeout/Error  
    WaitingForDeposition --> Error: Timeout/Error
    WaitingForGalvo --> Error: Timeout/Error
    
    Error --> Idle: Error Cleared
    Error --> [*]: System Reset
```

### State Transitions and Triggers

| Current State | Trigger | Next State | Actions |
|---------------|---------|------------|---------|
| Idle | Start Job Request | JobInit | Initialize job state, parse CLI |
| JobInit | Layer Upload Complete | WaitingForPrint | Send layer to Aerosint |
| WaitingForPrint | Aerosint = "printing" | RecoaterActive | Set recoater_ready_to_print=TRUE |
| RecoaterActive | PLC Movement Started | WaitingForDeposition | Monitor for completion |
| WaitingForDeposition | Aerosint = "ready" | WaitingForGalvo | Set recoater_layer_complete=TRUE |
| WaitingForGalvo | galvo_scan_complete | LayerComplete | Increment layer counter |
| LayerComplete | More layers exist | WaitingForPrint | Send next layer |
| LayerComplete | No more layers | JobComplete | Finalize job |
| Any State | Error/Timeout | Error | Pause all operations |

## Communication Protocols

### OPC UA Client Configuration

```python
class OPCUAConfig:
    ENDPOINT = "opc.tcp://localhost:4840"
    NAMESPACE = 2
    CONNECTION_TIMEOUT = 5.0
    SUBSCRIPTION_INTERVAL = 100  # milliseconds
    
    # Node addressing
    VARIABLE_NODES = {
        "job_active": "ns=2;s=job_active",
        "total_layers": "ns=2;s=total_layers",
        "current_layer": "ns=2;s=current_layer",
        # ... additional nodes
    }
```

### Aerosint API Integration

```python
class AerosintIntegration:
    BASE_URL = "http://*************:8080"
    
    # Key endpoints for multi-layer operation
    ENDPOINTS = {
        "upload_geometry": "PUT /drums/{drum_id}/geometry",
        "start_print": "POST /print/job", 
        "get_status": "GET /state",
        "cancel_print": "DELETE /print/job"
    }
    
    # Expected status values
    STATUS_READY = "ready"
    STATUS_PRINTING = "printing" 
    STATUS_ERROR = "error"
```

### Event-Driven Communication

**OPC UA Subscriptions:**
- Subscribe to `galvo_scan_complete` for automatic layer progression
- Subscribe to `system_error` for immediate error handling
- Real-time updates without polling overhead

**HTTP Polling for Aerosint:**
- Poll Aerosint status every 1 second during active operations
- Detect transitions from "printing" to "ready"
- Handle connection errors and timeouts

## Error Handling Architecture

### Error Categories and Responses

| Error Category | Detection Method | Response Strategy | AI Implementation Notes |
|----------------|------------------|-------------------|------------------------|
| **Backend Errors** | API failures, coordination issues, file errors | Set backend_error=TRUE, show persistent modal | **Critical: Must pause operation and show modal with 'X' button** |
| **PLC Errors** | Hardware faults, movement failures | PLC sets plc_error=TRUE | **Show PLC error modal, pause operations** |
| **Communication Errors** | Connection timeouts, network failures | Retry with exponential backoff, set backend_error if persistent | Use existing patterns from `recoater_client.py` |
| **User Errors** | Invalid CLI files, configuration issues | Reject operation, clear error message | Use FastAPI HTTPException with detailed messages |

### AI Agent Error Handling Patterns

**Recommended Error Handling Implementation:**

```python
# Use existing error classes from recoater_client.py
from services.recoater_client import RecoaterConnectionError, RecoaterAPIError

# Add new error classes for multi-material operations
class MultiMaterialJobError(Exception):
    """Base exception for multi-material job operations"""
    pass

class DrumSynchronizationError(MultiMaterialJobError):
    """Raised when drums fail to synchronize"""
    pass

class OPCUAConnectionError(Exception):
    """Raised when OPC UA connection fails"""
    pass

# Add critical API error class
class CriticalAPIError(MultiMaterialJobError):
    """Raised when API calls fail and require operator acknowledgment"""
    pass

# Implement consistent error handling patterns
async def handle_coordination_error(error: Exception, job_id: str):
    """Standard error handling for coordination failures"""
    logger.error(f"Coordination error in job {job_id}: {error}")
    # Pause job, notify operator, log diagnostics
    await pause_job_safely(job_id)
    await notify_operator(error)

async def handle_backend_error(error: Exception, job_id: str, operation: str):
    """Handle backend errors that require operator acknowledgment"""
    logger.error(f"Backend error in job {job_id} during {operation}: {error}")

    # Immediately pause all operations
    await pause_job_safely(job_id)

    # Set backend error state in OPC UA
    await opcua_server.set_variable("backend_error", True)

    # Send critical error to frontend for persistent modal display
    await websocket_manager.broadcast_critical_error({
        "type": "backend_error",
        "message": f"Backend Error: {operation} failed",
        "details": str(error),
        "job_id": job_id,
        "timestamp": time.time(),
        "requires_acknowledgment": True
    })

async def handle_plc_error_detection():
    """Monitor PLC error flag and handle when detected"""
    if await opcua_server.get_variable("plc_error"):
        logger.error("PLC error detected")

        # Pause all operations
        await pause_job_safely(multilayer_job_state.job_id)

        # Send PLC error to frontend
        await websocket_manager.broadcast_critical_error({
            "type": "plc_error",
            "message": "PLC Error - Check hardware systems",
            "details": "Hardware fault detected in PLC",
            "job_id": multilayer_job_state.job_id,
            "timestamp": time.time(),
            "requires_acknowledgment": True
        })

# Wrapper for all Aerosint API calls
async def safe_aerosint_api_call(operation: str, api_func, *args, **kwargs):
    """Wrapper for Aerosint API calls with critical error handling"""
    try:
        return await api_func(*args, **kwargs)
    except (RecoaterConnectionError, RecoaterAPIError) as e:
        await handle_backend_error(e, multilayer_job_state.job_id, operation)
        raise CriticalAPIError(f"{operation} failed: {e}")

# Clear error flags function for operator recovery
async def clear_error_flags():
    """Clear both backend_error and plc_error flags for operator recovery"""
    await opcua_server.set_variable("backend_error", False)
    await opcua_server.set_variable("plc_error", False)
    logger.info("Error flags cleared by operator")
```

### Error Recovery Mechanisms

**Automatic Recovery:**
- Network reconnection with retry logic
- Temporary communication failures
- Transient hardware faults

**Manual Recovery:**
- Hardware failures requiring intervention
- Configuration errors
- Safety-related stops
- **API call failures requiring operator acknowledgment**

**Recovery Process:**
1. **Detection**: Error identified by monitoring systems
2. **Isolation**: Stop current operations safely
3. **Notification**: Alert operator with persistent error modal
4. **Diagnosis**: Provide diagnostic information
5. **Recovery**: Allow manual error clearing or automatic retry

**Critical Error Modal Requirements:**
- **Persistent Display**: Modal remains visible until operator acknowledges
- **Prominent Styling**: Red/warning colors, large text, center screen
- **Error Type Distinction**:
  - Backend errors: Show backend error details and logs
  - PLC errors: Show "PLC Error - Check hardware systems"
- **'X' Button**: Allow operator to close modal and acknowledge error
- **Clear Error Flags Button**: Allow operator to reset backend_error and plc_error flags
- **Operation Pause**: All multi-material operations must pause until acknowledged
- **Error Logging**: Log all critical errors for debugging and audit trail

## Performance Considerations

### Communication Optimization

**OPC UA Optimization:**
- Use subscriptions instead of polling for real-time events
- Batch variable writes when possible
- Maintain persistent connections

**Aerosint Communication:**
- Efficient HTTP connection pooling
- Appropriate polling intervals (1-second for active operations)
- Timeout handling to prevent blocking

### Resource Management

**Memory Management:**
- CLI file caching with automatic cleanup
- Limit concurrent job processing
- Efficient data structures for layer management

**CPU Optimization:**
- Asynchronous processing for I/O operations
- Non-blocking coordination logic
- Efficient event handling

## Security Considerations

### Network Security
- OPC UA security policies and certificates
- HTTPS for Aerosint communication when available
- Network segmentation for industrial components

### Access Control
- Authentication for multi-layer job operations
- Role-based access for error clearing
- Audit logging for all operations

### Data Protection
- Secure storage of CLI files and job data
- Protection of coordination state information
- Safe handling of error messages and logs

## Scalability and Extensibility

### Future Enhancement Points

**Additional Recoater Support:**
- Multi-recoater coordination
- Load balancing across hardware
- Parallel processing capabilities

**Advanced Coordination:**
- Additional MCP server types
- Complex timing requirements
- Multi-step layer processing

**Monitoring and Analytics:**
- Performance metrics collection
- Predictive maintenance integration
- Advanced error analytics

## Integration Testing Strategy

### Component Integration Tests
1. **OPC UA Communication**: Verify bidirectional variable access
2. **Aerosint Integration**: Test all API operations and error conditions
3. **State Coordination**: Validate state machine transitions
4. **Error Handling**: Test all error scenarios and recovery

### End-to-End Testing
1. **Complete Multi-Layer Jobs**: Test full automation workflow
2. **Error Recovery**: Validate error handling and recovery procedures
3. **Performance Testing**: Verify timing and resource requirements
4. **Safety Testing**: Validate emergency stop and safety functions

This architecture provides a robust foundation for implementing automated multi-layer printing while maintaining system reliability and operator safety.
