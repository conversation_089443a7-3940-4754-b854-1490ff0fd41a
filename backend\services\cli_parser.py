"""
Service for parsing binary CLI files and rendering individual layers as PNG images.

This service is designed for integration into the RecoaterHMI backend. It operates on
in-memory byte streams and returns structured Pydantic models or raw PNG bytes,
avoiding direct filesystem I/O.

--- HOW TO USE THIS SERVICE IN THE HMI (FOR THE IMPLEMENTING AGENT) ---

The intended workflow for Phase 10 is as follows:

1.  **File Upload (New Endpoint `POST /api/v1/print/cli/upload`):**
    - The user uploads a binary CLI file from the frontend.
    - The backend endpoint receives the `UploadFile`.
    - The endpoint reads the file's content into memory: `file_bytes = await upload_file.read()`.
    - An instance of `CliParserService` is created: `parser = CliParserService()`.
    - The bytes are parsed: `parsed_data = parser.parse(cli_bytes)`.
    - The returned `ParsedCliFile` object is stored in a simple, server-side cache
      (an in-memory dictionary is sufficient), keyed by a newly generated unique ID (e.g., UUID).
    - The endpoint returns the unique ID and the number of layers (`len(parsed_data.layers)`) to the frontend.

2.  **Layer Preview (New Endpoint `GET /api/v1/print/cli/{file_id}/layer/{layer_num}/preview`):**
    - The frontend, having received the file ID and total layer count, provides a UI
      for the user to select a layer number.
    - When the user requests a preview, the frontend calls this new GET endpoint.
    - The backend retrieves the cached `ParsedCliFile` object using the `file_id`.
    - It selects the correct layer from the `parsed_data.layers` list (adjusting for zero-based index).
    - It calls the render method: `png_bytes = parser.render_layer_to_png(layer)`.
    - The endpoint returns the `png_bytes` in a `fastapi.Response` with `media_type="image/png"`.

This decouples the heavyweight parsing operation from the lightweight rendering operation,
providing an efficient user experience.
"""
import struct
import io
import logging
from typing import List, Tuple, Optional
from pydantic import BaseModel
from PIL import Image, ImageDraw

# --- Custom Exception ---
class CliParsingError(Exception):
    """Custom exception for errors during CLI file parsing."""
    pass

# --- Pydantic Models for Structured Data ---
class Point(BaseModel):
    """Represents a single 2D point."""
    x: float
    y: float

class Polyline(BaseModel):
    """Represents a polyline with multiple points."""
    part_id: int
    direction: int
    points: List[Point]

class Hatch(BaseModel):
    """Represents a set of hatch lines."""
    group_id: int
    lines: List[Tuple[Point, Point]]

class CliLayer(BaseModel):
    """Represents all geometry data for a single layer at a specific Z-height."""
    z_height: float
    polylines: List[Polyline] = []
    hatches: List[Hatch] = []

class ParsedCliFile(BaseModel):
    """Represents the entire parsed CLI file, including header and layers."""
    header_lines: List[str]
    is_aligned: bool
    layers: List[CliLayer]

# --- Main Service Class ---
class CliParserService:
    """A service to handle parsing of binary CLI files and rendering of layers."""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initializes the parser service."""
        self.logger = logger or logging.getLogger(__name__)

    def _read_and_unpack(self, stream: io.BytesIO, fmt: str, size: int) -> tuple:
        """Reads bytes from a stream and unpacks them."""
        data = stream.read(size)
        if len(data) < size:
            raise EOFError(f"Unexpected EOF. Wanted {size} bytes, got {len(data)}.")
        return struct.unpack(fmt, data)

    def parse(self, cli_byte_stream: bytes) -> ParsedCliFile:
        """Auto‐detect ASCII vs. binary CLI and delegate."""
        # Better detection logic: look for ASCII-specific patterns after header
        try:
            text = cli_byte_stream.decode("ascii", errors="strict")
            # Look for ASCII CLI patterns (commands followed by parameters on same line)
            # Support both space and slash delimited formats
            if "$$HEADEREND" in text and ("$$LAYER " in text or "$$POLYLINE " in text or "$$LAYER/" in text or "$$POLYLINE/" in text):
                return self._parse_ascii(text)
        except UnicodeDecodeError:
            pass  # Definitely binary
        
        # Default to binary parsing
        return self._parse_binary(cli_byte_stream)

    # (you can leave _looks_like_ascii around as a fallback or deprecate it)
    def _looks_like_ascii(self, data: bytes) -> bool:
        text = data.decode("ascii", errors="ignore")
        return "$$HEADEREND" in text and ("$$LAYER" in text or "$$POLYLINE" in text)

    def _parse_ascii(self, text: str) -> ParsedCliFile:
        """Line‐by‐line CLI parser for ASCII .cli files with better error handling.
        Supports both space-delimited and slash-delimited formats."""
        lines = [l.strip() for l in text.splitlines() if l.strip()]
        header_lines: List[str] = []
        geom_lines: List[str] = []
        seen_header_end = False
        seen_geometry_start = False

        # split header vs geometry
        for l in lines:
            if not seen_header_end:
                header_lines.append(l)
                if l.upper() == "$$HEADEREND":
                    seen_header_end = True
                continue
            # Skip $$GEOMETRYSTART if present
            if l.upper() == "$$GEOMETRYSTART":
                seen_geometry_start = True
                continue
            geom_lines.append(l)

        is_aligned = any("$$ALIGN" in h.upper() for h in header_lines)
        layers: List[CliLayer] = []
        current: Optional[CliLayer] = None
        i = 0

        def parse_command_line(line: str) -> tuple:
            """Parse a command line that can be either space or slash delimited."""
            if '/' in line and line.startswith('$$'):
                # Slash-delimited format: $$LAYER/16.0
                parts = line.split('/', 1)
                if len(parts) == 2:
                    cmd = parts[0].upper()
                    param_str = parts[1]
                    # For commands that need multiple parameters, split by comma or space
                    if cmd in ["$$POLYLINE", "$$HATCHES"]:
                        params = param_str.replace(',', ' ').split()
                        return [cmd] + params
                    else:
                        return [cmd, param_str]
                else:
                    return [line.upper()]
            else:
                # Space-delimited format: $$LAYER 16.0
                return line.split()

        while i < len(geom_lines):
            line = geom_lines[i]
            parts = parse_command_line(line)
            
            if not parts:  # Skip empty lines
                i += 1
                continue
                
            cmd = parts[0].upper()
            
            try:
                if cmd == "$$LAYER":
                    if len(parts) < 2:
                        raise CliParsingError(f"Invalid $$LAYER directive at line {i}: '{line}' - missing Z height")
                    z = float(parts[1])
                    current = CliLayer(z_height=z, polylines=[], hatches=[])
                    layers.append(current)
                    i += 1

                elif cmd == "$$POLYLINE":
                    if not current:
                        raise CliParsingError(f"Found $$POLYLINE at line {i} before any $$LAYER")
                    if len(parts) < 4:
                        raise CliParsingError(f"Invalid $$POLYLINE directive at line {i}: '{line}' - expected 3 args")
                    
                    try:
                        # Check if coordinates are inline (more than 4 parts) or on separate lines
                        if len(parts) > 4:
                            # Inline format: $$POLYLINE/part_id,direction,count,x1,y1,x2,y2,...
                            pid, dr, cnt = int(parts[1]), int(parts[2]), int(parts[3])
                            coord_parts = parts[4:]  # All coordinate values
                            
                            if len(coord_parts) < cnt * 2:
                                raise CliParsingError(f"Insufficient coordinates in polyline at line {i}: expected {cnt * 2} coordinates, got {len(coord_parts)}")
                            
                            pts: List[Point] = []
                            for coord_idx in range(0, cnt * 2, 2):
                                try:
                                    x = float(coord_parts[coord_idx])
                                    y = float(coord_parts[coord_idx + 1])
                                    pts.append(Point(x=x, y=y))
                                except (ValueError, IndexError) as e:
                                    raise CliParsingError(f"Invalid coordinates in polyline at line {i}: {e}")
                            
                            current.polylines.append(Polyline(part_id=pid, direction=dr, points=pts))
                            i += 1  # Only advance by 1 since all data is on current line
                            
                        else:
                            # Multi-line format: $$POLYLINE part_id direction count (coordinates on separate lines)
                            _, pid_s, dr_s, cnt_s = parts
                            pid, dr, cnt = int(pid_s), int(dr_s), int(cnt_s)
                            
                            pts: List[Point] = []
                            # read the next cnt lines as point coords
                            for j in range(1, cnt + 1):
                                point_line_idx = i + j
                                if point_line_idx >= len(geom_lines):
                                    raise CliParsingError(f"Unexpected EOF in polyline points at line {i}. Expected {cnt} points, but only found {j-1} points. Missing {cnt - (j-1)} points.")
                                
                                point_line = geom_lines[point_line_idx]
                                xy = point_line.replace(',', ' ').split()  # Handle comma-separated coordinates
                                if len(xy) < 2:
                                    raise CliParsingError(f"Bad polyline point at line {point_line_idx}: '{point_line}' - expected X Y coordinates")
                                
                                try:
                                    x, y = float(xy[0]), float(xy[1])
                                    pts.append(Point(x=x, y=y))
                                except ValueError as e:
                                    raise CliParsingError(f"Invalid coordinates at line {point_line_idx}: '{point_line}' - {e}")
                            
                            current.polylines.append(Polyline(part_id=pid, direction=dr, points=pts))
                            i += 1 + cnt  # Advance by 1 + number of coordinate lines
                            
                    except ValueError as e:
                        raise CliParsingError(f"Invalid $$POLYLINE parameters at line {i}: '{line}' - {e}")

                elif cmd == "$$HATCHES":
                    if not current:
                        raise CliParsingError(f"Found $$HATCHES at line {i} before any $$LAYER")
                    if len(parts) < 3:
                        raise CliParsingError(f"Invalid $$HATCHES directive at line {i}: '{line}' - expected 2 args")
                    
                    try:
                        # Check if coordinates are inline (more than 3 parts) or on separate lines
                        if len(parts) > 3:
                            # Inline format: $$HATCHES/group_id,count,x1,y1,x2,y2,x3,y3,x4,y4,...
                            grp, num = int(parts[1]), int(parts[2])
                            coord_parts = parts[3:]  # All coordinate values
                            
                            if len(coord_parts) < num * 4:
                                raise CliParsingError(f"Insufficient coordinates in hatches at line {i}: expected {num * 4} coordinates, got {len(coord_parts)}")
                            
                            lines_list: List[Tuple[Point, Point]] = []
                            for coord_idx in range(0, num * 4, 4):
                                try:
                                    x1 = float(coord_parts[coord_idx])
                                    y1 = float(coord_parts[coord_idx + 1])
                                    x2 = float(coord_parts[coord_idx + 2])
                                    y2 = float(coord_parts[coord_idx + 3])
                                    lines_list.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
                                except (ValueError, IndexError) as e:
                                    raise CliParsingError(f"Invalid coordinates in hatches at line {i}: {e}")
                            
                            current.hatches.append(Hatch(group_id=grp, lines=lines_list))
                            i += 1  # Only advance by 1 since all data is on current line
                            
                        else:
                            # Multi-line format: $$HATCHES group_id count (coordinates on separate lines)
                            _, grp_s, num_s = parts
                            grp, num = int(grp_s), int(num_s)
                            
                            lines_list: List[Tuple[Point, Point]] = []
                            for j in range(1, num + 1):
                                hatch_line_idx = i + j
                                if hatch_line_idx >= len(geom_lines):
                                    raise CliParsingError(f"Unexpected EOF in hatches at line {i}. Expected {num} hatch lines, but only found {j-1}.")
                                
                                hatch_line = geom_lines[hatch_line_idx]
                                coords = hatch_line.replace(',', ' ').split()  # Handle comma-separated coordinates
                                if len(coords) < 4:
                                    raise CliParsingError(f"Bad hatch line at line {hatch_line_idx}: '{hatch_line}' - expected X1 Y1 X2 Y2 coordinates")
                                
                                try:
                                    x1, y1, x2, y2 = map(float, coords[:4])
                                    lines_list.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
                                except ValueError as e:
                                    raise CliParsingError(f"Invalid hatch coordinates at line {hatch_line_idx}: '{hatch_line}' - {e}")
                            
                            current.hatches.append(Hatch(group_id=grp, lines=lines_list))
                            i += 1 + num  # Advance by 1 + number of coordinate lines
                            
                    except ValueError as e:
                        raise CliParsingError(f"Invalid $$HATCHES parameters at line {i}: '{line}' - {e}")

                else:
                    # unrecognized directive, skip with warning
                    self.logger.warning(f"Unrecognized directive at line {i}: '{line}' - skipping")
                    i += 1
                    
            except CliParsingError:
                raise  # Re-raise CLI parsing errors
            except Exception as e:
                raise CliParsingError(f"Unexpected error parsing line {i}: '{line}' - {e}")

        return ParsedCliFile(header_lines=header_lines, is_aligned=is_aligned, layers=layers)

    def _parse_binary(self, cli_byte_stream: bytes) -> ParsedCliFile:
        """
        Parses a binary CLI file from an in-memory byte stream.
        Fixed version with better header handling.
        """
        stream = io.BytesIO(cli_byte_stream)

        # 1. Parse Header - More robust approach
        header_end_marker = b"$$HEADEREND"
        header_buffer = bytearray()
        max_header_size = 8192
        
        while True:
            byte = stream.read(1)
            if not byte:
                raise CliParsingError(f"EOF reached before '{header_end_marker.decode()}' was found.")
            header_buffer.append(byte[0])
            
            # Check if we found the marker
            if header_end_marker in header_buffer:
                # Find the exact position of the marker
                marker_pos = header_buffer.find(header_end_marker)
                actual_header_bytes = header_buffer[:marker_pos + len(header_end_marker)]
                
                # Set stream position to after the header
                geometry_start_offset = stream.tell() - (len(header_buffer) - len(actual_header_bytes))
                stream.seek(geometry_start_offset)
                break
                
            if len(header_buffer) > max_header_size:
                raise CliParsingError(f"Header size exceeds {max_header_size} bytes limit.")

        # Process header
        try:
            header_str = header_buffer[:marker_pos + len(header_end_marker)].decode('ascii', errors='replace')
        except UnicodeDecodeError as e:
            self.logger.warning(f"Unicode decode error in header: {e}. Using replacement characters.")
            header_str = header_buffer[:marker_pos + len(header_end_marker)].decode('ascii', errors='replace')
        
        header_lines = [line.strip() for line in header_str.splitlines() if line.strip()]
        is_aligned = any("$$ALIGN" in line.upper() for line in header_lines)
        self.logger.info(f"CLI header parsed. Alignment detected: {is_aligned}")

        # 2. Parse Geometry
        layers: List[CliLayer] = []
        current_layer: Optional[CliLayer] = None

        while True:
            try:
                command_code = self._read_and_unpack(stream, "<H", 2)[0]
                if is_aligned:
                    stream.read(2)  # Skip 2 alignment bytes

                if command_code == 127:  # Layer
                    z = self._read_and_unpack(stream, "<f", 4)[0]
                    current_layer = CliLayer(z_height=z)
                    layers.append(current_layer)
                
                elif command_code == 130:  # Polyline
                    if not current_layer:
                        raise CliParsingError("Found Polyline data before a Layer was defined.")
                    part_id, direction, num_points = self._read_and_unpack(stream, "<iii", 12)
                    points = []
                    for _ in range(num_points):
                        x = self._read_and_unpack(stream, "<f", 4)[0]
                        y = self._read_and_unpack(stream, "<f", 4)[0]
                        points.append(Point(x=x, y=y))
                    current_layer.polylines.append(Polyline(part_id=part_id, direction=direction, points=points))

                elif command_code == 132:  # Hatches
                    if not current_layer:
                        raise CliParsingError("Found Hatch data before a Layer was defined.")
                    group_id, num_lines = self._read_and_unpack(stream, "<ii", 8)
                    hatches = []
                    for _ in range(num_lines):
                        x1, y1, x2, y2 = self._read_and_unpack(stream, "<ffff", 16)
                        hatches.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
                    current_layer.hatches.append(Hatch(group_id=group_id, lines=hatches))
                
                else:
                    self.logger.warning(f"Unsupported command code {command_code} at offset {stream.tell()}. Stopping parse.")
                    break

            except EOFError:
                self.logger.info("Successfully reached end of CLI geometry data.")
                break
            except struct.error as e:
                raise CliParsingError(f"Struct unpacking error at offset {stream.tell()}: {e}")

        return ParsedCliFile(header_lines=header_lines, is_aligned=is_aligned, layers=layers)

    def render_layer_to_png(self, layer: CliLayer, width: int = 800, height: int = 600, drum_id: int = None) -> bytes:
        """
        Renders a single CliLayer object to a PNG byte string with autoscaling.

        Args:
            layer: The CliLayer object to render.
            width: The width of the output PNG image.
            height: The height of the output PNG image.
            drum_id: Optional drum ID for single-drum preview (affects coloring)

        Returns:
            A byte string containing the PNG image data.
        """
        image = Image.new("RGB", (width, height), "white")
        draw = ImageDraw.Draw(image)

        # Define drum colors matching the Legend component
        DRUM_COLORS = {
            0: "#3498db",  # Blue - Drum 0
            1: "#e67e22",  # Orange - Drum 1
            2: "#27ae60"   # Green - Drum 2
        }

        all_points = [p for poly in layer.polylines for p in poly.points]
        for hatch in layer.hatches:
            all_points.extend([h[0] for h in hatch.lines])
            all_points.extend([h[1] for h in hatch.lines])

        if not all_points:
            draw.text((10, 10), "No geometry in this layer", fill="black")
        else:
            # Determine bounding box of the geometry
            min_x = min(p.x for p in all_points)
            max_x = max(p.x for p in all_points)
            min_y = min(p.y for p in all_points)
            max_y = max(p.y for p in all_points)

            geo_width = max_x - min_x
            geo_height = max_y - min_y
            
            # Handle zero-dimension case
            if geo_width == 0 and geo_height == 0:
                 draw.point([(width / 2, height / 2)], fill="black")
            else:
                if geo_width == 0: geo_width = 1
                if geo_height == 0: geo_height = 1

                # Calculate scale factor to fit geometry, with a margin
                margin = 0.1
                scale_x = (width * (1 - 2 * margin)) / geo_width
                scale_y = (height * (1 - 2 * margin)) / geo_height
                scale = min(scale_x, scale_y)

                # Calculate offsets to center the geometry
                offset_x = (width - geo_width * scale) / 2
                offset_y = (height - geo_height * scale) / 2

                def transform(p: Point) -> tuple:
                    # Y-axis is inverted in PIL
                    px = (p.x - min_x) * scale + offset_x
                    py = (max_y - p.y) * scale + offset_y
                    return (px, py)

                # Draw polylines with drum-specific colors
                for poly in layer.polylines:
                    if len(poly.points) > 1:
                        if drum_id is not None:
                            # Single drum preview - use the specific drum color
                            color = DRUM_COLORS.get(drum_id, "#000000")
                        else:
                            # Layer configuration preview - map part_id to drum color
                            mapped_drum = poly.part_id % 3  # Map part_id to drum 0, 1, or 2
                            color = DRUM_COLORS.get(mapped_drum, "#000000")

                        draw.line([transform(p) for p in poly.points], fill=color, width=2)

                # Draw hatches with drum-specific colors
                for hatch in layer.hatches:
                    for line in hatch.lines:
                        if drum_id is not None:
                            # Single drum preview - use the specific drum color
                            color = DRUM_COLORS.get(drum_id, "#000000")
                        else:
                            # Layer configuration preview - map group_id to drum color
                            mapped_drum = hatch.group_id % 3  # Map group_id to drum 0, 1, or 2
                            color = DRUM_COLORS.get(mapped_drum, "#000000")

                        draw.line((transform(line[0]), transform(line[1])), fill=color, width=1)

        # Save to in-memory buffer
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='PNG')
        return img_buffer.getvalue()

    def render_layer_configuration_preview(self, width: int = 800, height: int = 600) -> bytes:
        """
        Renders a preview of the current layer configuration showing how geometry
        would be distributed across drums using the defined color scheme.

        This creates a mock layer with sample geometry to demonstrate the color mapping.
        In a real implementation, this would use the actual current layer configuration.

        Args:
            width: The width of the output PNG image.
            height: The height of the output PNG image.

        Returns:
            A byte string containing the PNG image data.
        """
        # Create a sample layer with geometry for demonstration
        # In a real implementation, this would get the actual current layer configuration
        sample_layer = CliLayer(
            z_height=0.0,
            polylines=[
                # Sample polylines for each drum
                Polyline(part_id=0, direction=0, points=[
                    Point(x=10, y=10), Point(x=30, y=10), Point(x=30, y=30), Point(x=10, y=30), Point(x=10, y=10)
                ]),
                Polyline(part_id=1, direction=0, points=[
                    Point(x=40, y=10), Point(x=60, y=10), Point(x=60, y=30), Point(x=40, y=30), Point(x=40, y=10)
                ]),
                Polyline(part_id=2, direction=0, points=[
                    Point(x=70, y=10), Point(x=90, y=10), Point(x=90, y=30), Point(x=70, y=30), Point(x=70, y=10)
                ])
            ],
            hatches=[
                # Sample hatches for each drum
                Hatch(group_id=0, lines=[
                    (Point(x=15, y=15), Point(x=25, y=15)),
                    (Point(x=15, y=20), Point(x=25, y=20)),
                    (Point(x=15, y=25), Point(x=25, y=25))
                ]),
                Hatch(group_id=1, lines=[
                    (Point(x=45, y=15), Point(x=55, y=15)),
                    (Point(x=45, y=20), Point(x=55, y=20)),
                    (Point(x=45, y=25), Point(x=55, y=25))
                ]),
                Hatch(group_id=2, lines=[
                    (Point(x=75, y=15), Point(x=85, y=15)),
                    (Point(x=75, y=20), Point(x=85, y=20)),
                    (Point(x=75, y=25), Point(x=85, y=25))
                ])
            ]
        )

        # Render the sample layer without specifying a drum_id (uses color mapping)
        return self.render_layer_to_png(sample_layer, width, height, drum_id=None)

    def generate_single_layer_cli(self, layer: CliLayer, header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """
        Generates CLI file data for a single layer.

        Args:
            layer: The CliLayer object to convert to CLI format
            header_lines: Optional header lines to include (defaults to minimal header)
            is_aligned: Whether to use aligned format (defaults to False)

        Returns:
            A byte string containing the CLI file data for the single layer

        Raises:
            CliParsingError: If the layer data cannot be serialized
        """
        try:
            stream = io.BytesIO()

            # 1. Write Header
            if header_lines is None:
                header_lines = ["$$HEADEREND"]

            header_str = "\n".join(header_lines)
            if not header_str.endswith("$$HEADEREND"):
                header_str += "\n$$HEADEREND"

            stream.write(header_str.encode('ascii'))

            # 2. Write Layer Command (127)
            stream.write(struct.pack("<H", 127))  # Layer command code
            if is_aligned:
                stream.write(b'\x00\x00')  # Alignment bytes
            stream.write(struct.pack("<f", layer.z_height))  # Z height

            # 3. Write Polylines (130)
            for poly in layer.polylines:
                if len(poly.points) > 0:
                    stream.write(struct.pack("<H", 130))  # Polyline command code
                    if is_aligned:
                        stream.write(b'\x00\x00')  # Alignment bytes

                    # Write polyline header
                    stream.write(struct.pack("<iii", poly.part_id, poly.direction, len(poly.points)))

                    # Write points
                    for point in poly.points:
                        stream.write(struct.pack("<f", point.x))
                        stream.write(struct.pack("<f", point.y))

            # 4. Write Hatches (132)
            for hatch in layer.hatches:
                if len(hatch.lines) > 0:
                    stream.write(struct.pack("<H", 132))  # Hatch command code
                    if is_aligned:
                        stream.write(b'\x00\x00')  # Alignment bytes

                    # Write hatch header
                    stream.write(struct.pack("<ii", hatch.group_id, len(hatch.lines)))

                    # Write hatch lines
                    for line in hatch.lines:
                        stream.write(struct.pack("<ffff", line[0].x, line[0].y, line[1].x, line[1].y))

            return stream.getvalue()

        except Exception as e:
            raise CliParsingError(f"Failed to generate CLI data for layer: {e}")

    def generate_cli_from_layer_range(self, layers: List[CliLayer], header_lines: List[str] = None, is_aligned: bool = False) -> bytes:
        """
        Generates a complete binary CLI file from a range of layers.

        Args:
            layers: A list of CliLayer objects to include in the file.
            header_lines: Optional header lines. A minimal header is used if not provided.
            is_aligned: Whether to use the aligned format.

        Returns:
            A byte string containing the complete CLI file data.

        Raises:
            CliParsingError: If the layer list is empty or data cannot be serialized.
        """
        if not layers:
            raise CliParsingError("Cannot generate CLI data from an empty layer range.")

        try:
            stream = io.BytesIO()

            # 1. Write Header
            if header_lines is None:
                # Provide a default minimal header if none is given
                header_lines = ["$$HEADEREND"]

            # Ensure the header is correctly terminated
            header_str = "\n".join(header_lines)
            if not header_str.endswith("$$HEADEREND"):
                header_str += "\n$$HEADEREND"
            
            stream.write(header_str.encode('ascii'))

            # 2. Write each layer's data
            for layer in layers:
                # Write Layer Command (127)
                stream.write(struct.pack("<H", 127))
                if is_aligned:
                    stream.write(b'\x00\x00')
                stream.write(struct.pack("<f", layer.z_height))

                # Write Polylines (130)
                for poly in layer.polylines:
                    if len(poly.points) > 0:
                        stream.write(struct.pack("<H", 130))
                        if is_aligned:
                            stream.write(b'\x00\x00')
                        stream.write(struct.pack("<iii", poly.part_id, poly.direction, len(poly.points)))
                        for point in poly.points:
                            stream.write(struct.pack("<ff", point.x, point.y))

                # Write Hatches (132)
                for hatch in layer.hatches:
                    if len(hatch.lines) > 0:
                        stream.write(struct.pack("<H", 132))
                        if is_aligned:
                            stream.write(b'\x00\x00')
                        stream.write(struct.pack("<ii", hatch.group_id, len(hatch.lines)))
                        for line in hatch.lines:
                            stream.write(struct.pack("<ffff", line[0].x, line[0].y, line[1].x, line[1].y))

            return stream.getvalue()

        except Exception as e:
            raise CliParsingError(f"Failed to generate CLI data for layer range: {e}")

    def generate_single_layer_ascii_cli(self, layer: CliLayer, header_lines: List[str] = None) -> bytes:
        """
        Generates ASCII CLI file data for a single layer.

        This method creates ASCII CLI format that is compatible with the actual hardware,
        unlike the binary format which doesn't work with the recoater hardware.

        Args:
            layer: The CliLayer object to convert to ASCII CLI format
            header_lines: Optional header lines to include (defaults to minimal header)

        Returns:
            A byte string containing the ASCII CLI file data for the single layer

        Raises:
            CliParsingError: If the layer data cannot be serialized
        """
        try:
            lines = []

            # 1. Write Header
            if header_lines is None:
                # Use a minimal header similar to the example file
                header_lines = [
                    "$$HEADERSTART",
                    "$$ASCII",
                    "$$UNITS/00000000.005000",
                    "$$VERSION/200",
                    "$$LABEL/1,default",
                    "$$DATE/060623",
                    "$$DIMENSION/-0000020.000000,-0000020.000000,00000000.080000,00000020.000000,00000020.000000,00000040.000000",
                    "$$LAYERS/000001",
                    "$$HEADEREND"
                ]

            # Add header lines, but update the layer count for single layer
            for line in header_lines:
                if line.startswith("$$LAYERS/"):
                    # Update layer count to 1 for single layer
                    lines.append("$$LAYERS/000001")
                else:
                    lines.append(line)

            # Ensure we have $$GEOMETRYSTART after header
            if "$$GEOMETRYSTART" not in lines:
                lines.append("$$GEOMETRYSTART")

            # 2. Write Layer Command
            lines.append(f"$$LAYER/{layer.z_height}")

            # 3. Write Polylines
            for poly in layer.polylines:
                if len(poly.points) > 0:
                    # Format: $$POLYLINE/part_id,direction,num_points,x1,y1,x2,y2,...
                    point_coords = []
                    for point in poly.points:
                        point_coords.extend([f"{point.x:.5f}", f"{point.y:.5f}"])

                    polyline_data = f"$$POLYLINE/{poly.part_id},{poly.direction},{len(poly.points)},{','.join(point_coords)}"
                    lines.append(polyline_data)

            # 4. Write Hatches
            for hatch in layer.hatches:
                if len(hatch.lines) > 0:
                    # Format: $$HATCHES/group_id,num_lines,x1,y1,x2,y2,x3,y3,x4,y4,...
                    line_coords = []
                    for line in hatch.lines:
                        line_coords.extend([
                            f"{line[0].x:.5f}", f"{line[0].y:.5f}",
                            f"{line[1].x:.5f}", f"{line[1].y:.5f}"
                        ])

                    hatch_data = f"$$HATCHES/{hatch.group_id},{len(hatch.lines)},{','.join(line_coords)}"
                    lines.append(hatch_data)

            # Add geometry end
            lines.append("$$GEOMETRYEND")

            # Join all lines with newlines and encode to bytes
            cli_content = "\n".join(lines)
            return cli_content.encode('ascii')

        except Exception as e:
            raise CliParsingError(f"Failed to generate ASCII CLI data for layer: {e}")

    def generate_ascii_cli_from_layer_range(self, layers: List[CliLayer], header_lines: List[str] = None) -> bytes:
        """
        Generates a complete ASCII CLI file from a range of layers.

        This method creates ASCII CLI format that is compatible with the actual hardware,
        unlike the binary format which doesn't work with the recoater hardware.

        Args:
            layers: A list of CliLayer objects to include in the file.
            header_lines: Optional header lines. A minimal header is used if not provided.

        Returns:
            A byte string containing the complete ASCII CLI file data.

        Raises:
            CliParsingError: If the layer list is empty or data cannot be serialized.
        """
        if not layers:
            raise CliParsingError("Cannot generate ASCII CLI data from an empty layer range.")

        try:
            lines = []

            # 1. Write Header
            if header_lines is None:
                # Use a minimal header similar to the example file
                header_lines = [
                    "$$HEADERSTART",
                    "$$ASCII",
                    "$$UNITS/00000000.005000",
                    "$$VERSION/200",
                    "$$LABEL/1,default",
                    "$$DATE/060623",
                    "$$DIMENSION/-0000020.000000,-0000020.000000,00000000.080000,00000020.000000,00000020.000000,00000040.000000",
                    f"$$LAYERS/{len(layers):06d}",
                    "$$HEADEREND"
                ]

            # Add header lines, but update the layer count
            for line in header_lines:
                if line.startswith("$$LAYERS/"):
                    # Update layer count to match the actual number of layers
                    lines.append(f"$$LAYERS/{len(layers):06d}")
                else:
                    lines.append(line)

            # Ensure we have $$GEOMETRYSTART after header
            if "$$GEOMETRYSTART" not in lines:
                lines.append("$$GEOMETRYSTART")

            # 2. Write each layer's data
            for layer in layers:
                # Write Layer Command
                lines.append(f"$$LAYER/{layer.z_height}")

                # Write Polylines
                for poly in layer.polylines:
                    if len(poly.points) > 0:
                        # Format: $$POLYLINE/part_id,direction,num_points,x1,y1,x2,y2,...
                        point_coords = []
                        for point in poly.points:
                            point_coords.extend([f"{point.x:.5f}", f"{point.y:.5f}"])

                        polyline_data = f"$$POLYLINE/{poly.part_id},{poly.direction},{len(poly.points)},{','.join(point_coords)}"
                        lines.append(polyline_data)

                # Write Hatches
                for hatch in layer.hatches:
                    if len(hatch.lines) > 0:
                        # Format: $$HATCHES/group_id,num_lines,x1,y1,x2,y2,x3,y3,x4,y4,...
                        line_coords = []
                        for line in hatch.lines:
                            line_coords.extend([
                                f"{line[0].x:.5f}", f"{line[0].y:.5f}",
                                f"{line[1].x:.5f}", f"{line[1].y:.5f}"
                            ])

                        hatch_data = f"$$HATCHES/{hatch.group_id},{len(hatch.lines)},{','.join(line_coords)}"
                        lines.append(hatch_data)

            # Add geometry end
            lines.append("$$GEOMETRYEND")

            # Join all lines with newlines and encode to bytes
            cli_content = "\n".join(lines)
            return cli_content.encode('ascii')

        except Exception as e:
            raise CliParsingError(f"Failed to generate ASCII CLI data for layer range: {e}")
