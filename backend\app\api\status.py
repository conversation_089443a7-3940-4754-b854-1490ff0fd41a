"""
Status API Router
================

This module provides the status-related API endpoints for the Recoater HMI.
It handles requests for system status and connection health checks.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import logging

from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError
from app.dependencies import get_recoater_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/status", tags=["status"])

@router.get("/")
async def get_status(client: RecoaterClient = Depends(get_recoater_client)) -> Dict[str, Any]:
    """
    Get the current status of the recoater system.
    
    This endpoint provides the current state of the recoater hardware,
    including system status, operational state, and any error conditions.
    
    Returns:
        Dictionary containing the recoater status information
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info("Getting recoater status")
        status_data = client.get_state()
        
        # Add connection status to the response
        response = {
            "connected": True,
            "recoater_status": status_data,
            "backend_status": "operational"
        }
        
        logger.debug(f"Status retrieved successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error while getting status: {e}")
        # Return partial status indicating connection failure
        return {
            "connected": False,
            "recoater_status": None,
            "backend_status": "operational",
            "error": "Failed to connect to recoater hardware",
            "error_details": str(e)
        }
        
    except RecoaterAPIError as e:
        logger.error(f"API error while getting status: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Recoater API error: {str(e)}"
        )
        
    except Exception as e:
        logger.error(f"Unexpected error while getting status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/health")
async def health_check(client: RecoaterClient = Depends(get_recoater_client)) -> Dict[str, Any]:
    """
    Perform a health check of the recoater connection.
    
    This endpoint provides a simple way to check if the backend can
    communicate with the recoater hardware.
    
    Returns:
        Dictionary containing health status information
    """
    try:
        logger.info("Performing health check")
        is_healthy = client.health_check()
        
        response = {
            "backend_healthy": True,
            "recoater_healthy": is_healthy,
            "overall_healthy": is_healthy
        }
        
        logger.debug(f"Health check completed: {response}")
        return response
        
    except Exception as e:
        logger.error(f"Error during health check: {e}")
        return {
            "backend_healthy": True,
            "recoater_healthy": False,
            "overall_healthy": False,
            "error": str(e)
        }


@router.post("/state")
async def set_server_state(
    action: str,
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the recoater server state (restart or shutdown).

    This endpoint allows restarting or shutting down the recoater server.

    Args:
        action: The action to perform ('restart' or 'shutdown')

    Returns:
        Dictionary containing the operation status

    Raises:
        HTTPException: If action is invalid or operation fails
    """
    if action not in ["restart", "shutdown"]:
        raise HTTPException(
            status_code=400,
            detail="Invalid action. Must be 'restart' or 'shutdown'"
        )

    try:
        logger.info(f"Setting server state: {action}")
        result = client.set_state(action)

        response = {
            "success": True,
            "action": action,
            "status": "accepted",
            "message": f"Server {action} command accepted"
        }

        logger.info(f"Server state change completed: {response}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Server state change connection error: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"Server state change API error: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error during server state change: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")
