<template>
  <div class="recoater-view">
    <!-- Header -->
    <div class="view-header">
      <h1 class="view-title">Recoater Dashboard</h1>
      <p class="view-subtitle">Monitor and control all recoater hardware components from this central dashboard.</p>
    </div>

    <!-- Connection Status -->
    <div class="status-card mb-12">
      <div class="status-header">
        <h3 class="status-title">Connection Status</h3>
        <div class="status-indicator">
          <div 
            :class="[
              'status-dot',
              statusStore.isConnected ? 'status-connected' : 'status-disconnected'
            ]"
          ></div>
          <span class="status-text">
            {{ statusStore.isConnected ? 'Connected' : 'Disconnected' }}
          </span>
        </div>
      </div>
      <div v-if="statusStore.lastError && !statusStore.isConnected" class="mt-2 text-sm text-red-600">
        Error: {{ statusStore.lastError }}
      </div>
    </div>

    <!-- Main Content Area -->
    <div v-if="statusStore.isConnected && statusStore.drumData && Object.keys(statusStore.drumData).length > 0">
      
      <!-- Drums Section -->
      <div class="section-container">
        <h2 class="section-title">Drum Controls</h2>
        <div class="dashboard-grid">
          <DrumControl
            v-for="(drumInfo, drumId) in statusStore.drumData"
            :key="drumId"
            :drum-id="parseInt(drumId)"
            :drum-status="drumInfo?.info || {}"
            :motion-data="drumInfo?.motion || {}"
            :ejection-data="drumInfo?.ejection || {}"
            :suction-data="drumInfo?.suction || {}"
            :connected="statusStore.isConnected"
            @error="handleError"
            @success="handleSuccess"
            @motion-started="handleMotionStarted"
            @motion-cancelled="handleMotionCancelled"
            @pressure-set="handlePressureSet"
          />
        </div>
      </div>

      <!-- Hopper Controls Section -->
      <div class="section-container">
        <h2 class="section-title">Hopper Controls (Scraping Blades)</h2>
        <div class="dashboard-grid">
          <HopperControl
            v-for="(drumInfo, drumId) in statusStore.drumData"
            :key="`hopper-${drumId}`"
            :drum-id="parseInt(drumId)"
            :blade-screws="drumInfo?.blade_screws || []"
            :blade-motion="drumInfo?.blade_motion || {}"
            :connected="statusStore.isConnected"
            @error="handleError"
            @success="handleSuccess"
            @motion-started="handleMotionStarted"
            @motion-cancelled="handleMotionCancelled"
          />
        </div>
      </div>

      <!-- Leveler Control Section -->
      <div class="section-container">
         <h2 class="section-title">Leveler Control</h2>
        <LevelerControl
            :pressure-data="statusStore.levelerData?.pressure || {}"
            :sensor-data="statusStore.levelerData?.sensor || {}"
            :connected="statusStore.isConnected"
            @error="handleError"
            @success="handleSuccess"
            @pressure-set="handlePressureSet"
        />
      </div>
    </div>
    
    <!-- No Drums Available -->
    <div v-else-if="statusStore.isConnected && (!statusStore.drumData || Object.keys(statusStore.drumData).length === 0)" class="placeholder-container">
      <div class="placeholder-icon">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>
      </div>
      <h3 class="placeholder-title">No Drums Available</h3>
      <p class="placeholder-text">No drum data is currently available from the recoater. This may be normal if the hardware is initializing.</p>
    </div>

    <!-- Loading State -->
    <div v-else-if="!statusStore.isConnected" class="placeholder-container">
      <div class="placeholder-icon">
        <svg class="animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
      </div>
      <h3 class="placeholder-title">Connecting...</h3>
      <p class="placeholder-text">Attempting to connect to the recoater backend.</p>
    </div>

    <!-- Global Notifications -->
    <div v-if="globalError" class="message message-error">
      <span>{{ globalError }}</span>
      <button @click="globalError = ''" class="close-btn">×</button>
    </div>
    <div v-if="successMessage" class="message message-success">
      <span>{{ successMessage }}</span>
      <button @click="successMessage = ''" class="close-btn">×</button>
    </div>
  </div>
</template>

<script>
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { useStatusStore } from '../stores/status';
import DrumControl from '../components/DrumControl.vue';
import HopperControl from '../components/HopperControl.vue';
import LevelerControl from '../components/LevelerControl.vue';

export default {
  name: 'RecoaterView',
  components: { DrumControl, HopperControl, LevelerControl },
  setup() {
    const statusStore = useStatusStore();
    const globalError = ref('');
    const successMessage = ref('');
    let errorTimeout = null;
    let successTimeout = null;

    // Auto-clear watchers
    watch(globalError, (newError) => {
      if (newError) {
        if (errorTimeout) clearTimeout(errorTimeout);
        errorTimeout = setTimeout(() => {
          globalError.value = '';
        }, 5000);
      }
    });

    watch(successMessage, (newMessage) => {
      if (newMessage) {
        if (successTimeout) clearTimeout(successTimeout);
        successTimeout = setTimeout(() => {
          successMessage.value = '';
        }, 3000); // Success messages clear faster
      }
    });

    const showMessage = (message, isError = false) => {
      if (isError) {
        globalError.value = message;
        successMessage.value = '';
      } else {
        successMessage.value = message;
        globalError.value = '';
      }
    };
    
    // Generic event handlers
    const handleError = (error) => {
      console.error('A component reported an error:', error);
      const detail = error.response?.data?.detail || 'An unknown error occurred.';
      showMessage(detail, true);
    };

    const handleSuccess = (message) => {
      console.log('A component reported success:', message);
      showMessage(message, false);
    };

    // Specific event handlers
    const handleMotionStarted = (event) => {
      const message = `Motion started for Drum ${event.drumId}`;
      showMessage(message, false);
    };

    const handleMotionCancelled = (event) => {
      const message = `Motion cancelled for Drum ${event.drumId}`;
      showMessage(message, false);
    };

    const handlePressureSet = (event) => {
      const message = `${event.type || 'ejection'} pressure set for Drum ${event.drumId}`;
      showMessage(message, false);
    };

    // Lifecycle hooks
    onMounted(() => {
      // WebSocket connection is now managed globally
      // No need to manually connect here
    });
    
    onUnmounted(() => {
      statusStore.disconnectWebSocket();
    });

    return {
      statusStore,
      globalError,
      successMessage,
      handleError,
      handleSuccess,
      handleMotionStarted,
      handleMotionCancelled,
      handlePressureSet,
    };
  }
};
</script>

<style scoped>
.recoater-view {
  padding: 1.5rem;
  background-color: #f9fafb;
  min-height: 100%;
}

.view-header {
  margin-bottom: 2rem;
}

.view-title {
  margin: 0;
  color: #111827;
  font-size: 2rem;
  font-weight: 650;
}

.view-subtitle {
  color: #6b7280;
  font-size: 1rem;
  margin-top: 0.25rem;
}

/* Status Card Styles */
.status-card {
  background: white;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  border: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.status-title {
  margin: 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}
.status-indicator { display: flex; align-items: center; gap: 0.5rem; }
.status-dot { width: 10px; height: 10px; border-radius: 50%; }
.status-connected { background-color: #10b981; }
.status-disconnected { background-color: #ef4444; }
.status-text { font-weight: 500; color: #374151; }

.section-container {
  margin-bottom: 2.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 1.5rem;
}

/* Placeholder for loading/no-data states */
.placeholder-container {
  text-align: center;
  padding: 4rem 2rem;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}
.placeholder-icon {
  margin: 0 auto 1rem auto;
  color: #9ca3af;
  width: 3rem;
  height: 3rem;
}
.placeholder-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}
.placeholder-text {
  font-size: 1rem;
  color: #6b7280;
  max-width: 400px;
  margin: 0.5rem auto 0 auto;
}

/* Message Styles */
.message {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
  max-width: 400px;
}
.message-success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}
.message-error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}
.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  margin-left: 1.5rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  padding: 0;
}
.close-btn:hover {
  opacity: 1;
}
</style>