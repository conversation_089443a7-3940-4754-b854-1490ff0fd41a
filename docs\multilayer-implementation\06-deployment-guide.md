# Deployment Guide: Multi-Layer Print Job System

## Overview

This document provides comprehensive deployment instructions for the multi-layer print job system, covering development, staging, and production environments. The deployment process ensures system reliability, security, and maintainability.

## Deployment Architecture

### Environment Overview

```mermaid
graph TB
    subgraph "Development Environment"
        DEV_FE[Vue.js Dev Server]
        DEV_BE[FastAPI Dev Server]
        DEV_PLC[PLC Simulator]
        DEV_MOCK[Mock Aerosint]
    end
    
    subgraph "Staging Environment"
        STAGE_FE[Vue.js Build]
        STAGE_BE[FastAPI Server]
        STAGE_PLC[Test PLC]
        STAGE_AER[Test Aerosint]
    end
    
    subgraph "Production Environment"
        PROD_FE[Vue.js Production]
        PROD_BE[FastAPI Production]
        PROD_PLC[Production PLC]
        PROD_AER[Production Aerosint]
        PROD_MON[Monitoring]
    end
```

### Deployment Environments

| Environment | Purpose | Characteristics |
|-------------|---------|----------------|
| **Development** | Active development and testing | Mock services, hot reload, debug mode |
| **Staging** | Pre-production validation | Production-like setup, real hardware testing |
| **Production** | Live system operation | High availability, monitoring, security |

## Prerequisites

### Infrastructure Requirements

**Hardware Requirements:**
- **Backend Server**: 4+ CPU cores, 8GB+ RAM, 100GB+ storage
- **Frontend Hosting**: CDN or web server capable of serving static files
- **Network**: Stable connection between all components
- **PLC**: TwinCAT runtime with OPC UA server enabled

**Software Dependencies:**
- **Python**: 3.8+ for backend
- **Node.js**: 16+ for frontend build
- **Docker**: For containerized deployment (recommended)
- **Nginx**: For reverse proxy and static file serving
- **SSL Certificates**: For HTTPS in production

### Network Configuration

**Required Network Connectivity:**
```
Backend Server ↔ TwinCAT PLC (OPC UA - Port 4840)
Backend Server ↔ Aerosint Server (HTTP - Port 8080)
Frontend ↔ Backend Server (HTTP/HTTPS - Port 8000)
Operators ↔ Frontend (HTTP/HTTPS - Port 80/443)
```

**Firewall Configuration:**
- Allow inbound connections on required ports
- Restrict access to administrative interfaces
- Enable logging for security monitoring

## Development Environment Setup

### Local Development

#### Backend Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install development dependencies
pip install pytest pytest-asyncio pytest-mock black flake8

# Create environment configuration
cp .env.example .env

# Edit .env file with development settings
```

**Development Environment Variables (`.env`):**
```env
# Application Settings
DEBUG=true
ENVIRONMENT=development
LOG_LEVEL=DEBUG

# OPC UA Configuration
OPCUA_ENDPOINT=opc.tcp://localhost:4840
OPCUA_NAMESPACE=2
OPCUA_TIMEOUT=5.0

# Aerosint Configuration
AEROSINT_BASE_URL=http://localhost:8080
AEROSINT_TIMEOUT=5.0

# Database (if needed)
DATABASE_URL=sqlite:///./dev.db

# Security (development only - change for production)
SECRET_KEY=dev-secret-key-change-in-production
```

#### Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Install development dependencies
npm install -D @vitejs/plugin-vue vitest @vue/test-utils

# Create environment configuration
cp .env.example .env.local

# Edit .env.local with development settings
```

**Frontend Environment Variables (`.env.local`):**
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000

# Development Settings
VITE_DEBUG=true
VITE_LOG_LEVEL=debug
```

#### Running Development Servers

```bash
# Terminal 1: Start backend
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Start frontend
cd frontend
npm run dev

# Terminal 3: Start OPC UA simulator (if needed)
# Follow PLC simulator setup instructions
```

### Docker Development Environment

#### Docker Compose Configuration (`docker-compose.dev.yml`)

```yaml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/venv
    environment:
      - DEBUG=true
      - OPCUA_ENDPOINT=opc.tcp://opcua-simulator:4840
      - AEROSINT_BASE_URL=http://aerosint-mock:8080
    depends_on:
      - opcua-simulator
      - aerosint-mock
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    restart: unless-stopped

  opcua-simulator:
    image: opcua/opcua-server:latest
    ports:
      - "4840:4840"
    volumes:
      - ./dev-config/opcua-config.xml:/config/server.xml
    restart: unless-stopped

  aerosint-mock:
    build:
      context: ./tests/mocks/aerosint-server
    ports:
      - "8080:8080"
    environment:
      - MOCK_MODE=true
    restart: unless-stopped

volumes:
  backend_deps:
  frontend_deps:
```

**Running Docker Development Environment:**
```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

## Staging Environment Deployment

### Staging Infrastructure

#### Docker Compose Configuration (`docker-compose.staging.yml`)

```yaml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/staging.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - backend
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    expose:
      - "8000"
    environment:
      - DEBUG=false
      - ENVIRONMENT=staging
      - OPCUA_ENDPOINT=opc.tcp://test-plc:4840
      - AEROSINT_BASE_URL=http://test-aerosint:8080
      - LOG_LEVEL=INFO
    env_file:
      - .env.staging
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped

  redis:
    image: redis:alpine
    expose:
      - "6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  monitoring:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped

volumes:
  redis_data:
```

### Nginx Configuration (`nginx/staging.conf`)

```nginx
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    server {
        listen 80;
        server_name staging.recoater-hmi.local;

        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name staging.recoater-hmi.local;

        # SSL Configuration
        ssl_certificate /etc/ssl/certs/staging.crt;
        ssl_certificate_key /etc/ssl/certs/staging.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        # Frontend static files
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
            
            # Security headers
            add_header X-Frame-Options DENY;
            add_header X-Content-Type-Options nosniff;
            add_header X-XSS-Protection "1; mode=block";
        }

        # API proxy
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # WebSocket proxy
        location /ws/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }

        # Health check
        location /health {
            proxy_pass http://backend/health;
        }
    }
}
```

### Deployment Scripts

#### Staging Deployment Script (`scripts/deploy-staging.sh`)

```bash
#!/bin/bash

set -e

echo "Starting staging deployment..."

# Configuration
ENVIRONMENT="staging"
PROJECT_DIR="/opt/recoater-hmi"
BACKUP_DIR="/opt/backups/recoater-hmi"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create backup
echo "Creating backup..."
mkdir -p $BACKUP_DIR
docker-compose -f docker-compose.staging.yml down
tar -czf $BACKUP_DIR/backup_$TIMESTAMP.tar.gz $PROJECT_DIR

# Update code
echo "Updating code..."
cd $PROJECT_DIR
git pull origin main

# Build frontend
echo "Building frontend..."
cd frontend
npm ci --production
npm run build

# Build backend
echo "Building backend..."
cd ../backend
docker build -t recoater-hmi-backend:$TIMESTAMP -f Dockerfile.prod .
docker tag recoater-hmi-backend:$TIMESTAMP recoater-hmi-backend:latest

# Update configuration
echo "Updating configuration..."
cp .env.staging.example .env.staging
# Note: Edit .env.staging with actual staging values

# Deploy services
echo "Deploying services..."
docker-compose -f docker-compose.staging.yml up -d

# Health check
echo "Performing health check..."
sleep 30
curl -f http://localhost/health || (echo "Health check failed" && exit 1)

echo "Staging deployment completed successfully!"
```

## Production Environment Deployment

### Production Infrastructure

#### High Availability Setup

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/prod.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
      - ./frontend/dist:/usr/share/nginx/html
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend-1
      - backend-2
    restart: unless-stopped
    networks:
      - frontend
      - backend

  backend-1:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    expose:
      - "8000"
    environment:
      - DEBUG=false
      - ENVIRONMENT=production
      - INSTANCE_ID=backend-1
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    networks:
      - backend
      - database

  backend-2:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    expose:
      - "8000"
    environment:
      - DEBUG=false
      - ENVIRONMENT=production
      - INSTANCE_ID=backend-2
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    networks:
      - backend
      - database

  redis:
    image: redis:alpine
    expose:
      - "6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - database

  prometheus:
    image: prom/prometheus
    ports:
      - "127.0.0.1:9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped
    networks:
      - monitoring

  grafana:
    image: grafana/grafana
    ports:
      - "127.0.0.1:3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    restart: unless-stopped
    networks:
      - monitoring

  log-aggregator:
    image: fluent/fluentd:latest
    volumes:
      - ./logs:/fluentd/log
      - ./monitoring/fluentd.conf:/fluentd/etc/fluent.conf
    restart: unless-stopped
    networks:
      - monitoring

networks:
  frontend:
  backend:
  database:
  monitoring:

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
```

### Production Configuration

#### Environment Variables (`.env.production`)

```env
# Application Settings
DEBUG=false
ENVIRONMENT=production
LOG_LEVEL=INFO
SECRET_KEY=your-super-secure-secret-key-here

# OPC UA Configuration
OPCUA_ENDPOINT=opc.tcp://production-plc:4840
OPCUA_NAMESPACE=2
OPCUA_TIMEOUT=10.0
OPCUA_RETRY_ATTEMPTS=3

# Aerosint Configuration
AEROSINT_BASE_URL=http://production-aerosint:8080
AEROSINT_TIMEOUT=10.0
AEROSINT_RETRY_ATTEMPTS=3

# Security
ALLOWED_HOSTS=production.recoater-hmi.com,*.recoater-hmi.com
CORS_ORIGINS=https://production.recoater-hmi.com

# Database
REDIS_URL=redis://redis:6379/0

# Monitoring
PROMETHEUS_METRICS_ENABLED=true
GRAFANA_PASSWORD=secure-grafana-password
```

#### Production Nginx Configuration (`nginx/prod.conf`)

```nginx
events {
    worker_connections 2048;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=1r/s;

    # Backend upstream
    upstream backend {
        least_conn;
        server backend-1:8000 max_fails=3 fail_timeout=30s;
        server backend-2:8000 max_fails=3 fail_timeout=30s;
    }

    server {
        listen 80;
        server_name production.recoater-hmi.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name production.recoater-hmi.com;

        # SSL Configuration
        ssl_certificate /etc/ssl/certs/production.crt;
        ssl_certificate_key /etc/ssl/certs/production.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Security headers
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";

        # Frontend static files
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # API endpoints
        location /api/ {
            limit_req zone=api burst=10 nodelay;
            
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;
        }

        # Authentication endpoints (stricter rate limiting)
        location /api/auth/ {
            limit_req zone=auth burst=3 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket connections
        location /ws/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket timeout
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
        }

        # Health check
        location /health {
            access_log off;
            proxy_pass http://backend/health;
        }

        # Block access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
}
```

### Production Deployment Scripts

#### Automated Deployment Script (`scripts/deploy-production.sh`)

```bash
#!/bin/bash

set -e

# Configuration
ENVIRONMENT="production"
PROJECT_DIR="/opt/recoater-hmi"
BACKUP_DIR="/opt/backups/recoater-hmi"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
ROLLBACK_FILE="$BACKUP_DIR/rollback_info_$TIMESTAMP.txt"

# Logging
LOG_FILE="/var/log/recoater-hmi-deploy.log"
exec > >(tee -a $LOG_FILE)
exec 2>&1

echo "=========================================="
echo "Production Deployment Started: $(date)"
echo "Timestamp: $TIMESTAMP"
echo "=========================================="

# Pre-deployment checks
echo "Performing pre-deployment checks..."

# Check if services are running
if ! docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
    echo "Warning: Some services are not running. Continuing with deployment..."
fi

# Check disk space
AVAILABLE_SPACE=$(df / | awk 'NR==2 {print $4}')
if [ $AVAILABLE_SPACE -lt 5000000 ]; then
    echo "Error: Insufficient disk space (less than 5GB available)"
    exit 1
fi

# Check network connectivity
if ! curl -f http://localhost/health >/dev/null 2>&1; then
    echo "Warning: Current health check failed. Proceeding with deployment..."
fi

# Create backup
echo "Creating backup..."
mkdir -p $BACKUP_DIR

# Stop services gracefully
echo "Stopping services gracefully..."
docker-compose -f docker-compose.prod.yml stop

# Create backup
tar -czf $BACKUP_DIR/backup_$TIMESTAMP.tar.gz \
    --exclude='node_modules' \
    --exclude='venv' \
    --exclude='*.pyc' \
    --exclude='logs/*' \
    $PROJECT_DIR

# Save current state for rollback
docker-compose -f docker-compose.prod.yml config > $BACKUP_DIR/docker-compose-backup-$TIMESTAMP.yml
echo "BACKUP_TIMESTAMP=$TIMESTAMP" > $ROLLBACK_FILE
echo "BACKUP_PATH=$BACKUP_DIR/backup_$TIMESTAMP.tar.gz" >> $ROLLBACK_FILE

# Update code
echo "Updating code..."
cd $PROJECT_DIR

# Stash any local changes
git stash push -m "Auto-stash before deployment $TIMESTAMP"

# Pull latest code
git pull origin main

# Verify code integrity
if ! git rev-parse HEAD >/dev/null 2>&1; then
    echo "Error: Git repository is in an inconsistent state"
    exit 1
fi

# Build frontend
echo "Building frontend..."
cd frontend

# Install dependencies
npm ci --production --silent

# Build production assets
npm run build

if [ ! -d "dist" ]; then
    echo "Error: Frontend build failed - dist directory not found"
    exit 1
fi

# Build backend
echo "Building backend..."
cd ../backend

# Build Docker image
BACKEND_IMAGE="recoater-hmi-backend:$TIMESTAMP"
docker build -t $BACKEND_IMAGE -f Dockerfile.prod .

if [ $? -ne 0 ]; then
    echo "Error: Backend Docker build failed"
    exit 1
fi

# Tag as latest
docker tag $BACKEND_IMAGE recoater-hmi-backend:latest

# Update configuration
echo "Updating configuration..."
if [ ! -f ".env.production" ]; then
    echo "Error: .env.production file not found"
    exit 1
fi

# Validate configuration
if ! grep -q "SECRET_KEY" .env.production; then
    echo "Error: SECRET_KEY not found in production configuration"
    exit 1
fi

# Deploy services
echo "Deploying services..."
cd $PROJECT_DIR

# Start services
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to start
echo "Waiting for services to start..."
sleep 60

# Health checks
echo "Performing health checks..."
HEALTH_CHECK_ATTEMPTS=10
HEALTH_CHECK_INTERVAL=30

for i in $(seq 1 $HEALTH_CHECK_ATTEMPTS); do
    echo "Health check attempt $i/$HEALTH_CHECK_ATTEMPTS..."
    
    if curl -f http://localhost/health >/dev/null 2>&1; then
        echo "Health check passed!"
        break
    fi
    
    if [ $i -eq $HEALTH_CHECK_ATTEMPTS ]; then
        echo "Error: Health checks failed after $HEALTH_CHECK_ATTEMPTS attempts"
        echo "Initiating rollback..."
        ./scripts/rollback-production.sh $TIMESTAMP
        exit 1
    fi
    
    sleep $HEALTH_CHECK_INTERVAL
done

# Verify specific endpoints
echo "Verifying API endpoints..."
if ! curl -f http://localhost/api/v1/health >/dev/null 2>&1; then
    echo "Warning: API health endpoint not responding"
fi

# Clean up old Docker images
echo "Cleaning up old Docker images..."
docker image prune -f

# Log deployment success
echo "=========================================="
echo "Production Deployment Completed Successfully: $(date)"
echo "Backup created: $BACKUP_DIR/backup_$TIMESTAMP.tar.gz"
echo "To rollback, run: ./scripts/rollback-production.sh $TIMESTAMP"
echo "=========================================="

# Send notification (if configured)
if [ -n "$SLACK_WEBHOOK" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"Recoater HMI production deployment completed successfully"}' \
        $SLACK_WEBHOOK
fi
```

#### Rollback Script (`scripts/rollback-production.sh`)

```bash
#!/bin/bash

set -e

TIMESTAMP=$1

if [ -z "$TIMESTAMP" ]; then
    echo "Usage: $0 <timestamp>"
    echo "Available backups:"
    ls -la /opt/backups/recoater-hmi/backup_*.tar.gz
    exit 1
fi

BACKUP_DIR="/opt/backups/recoater-hmi"
PROJECT_DIR="/opt/recoater-hmi"
ROLLBACK_INFO="$BACKUP_DIR/rollback_info_$TIMESTAMP.txt"

echo "Starting rollback to timestamp: $TIMESTAMP"

if [ ! -f "$ROLLBACK_INFO" ]; then
    echo "Error: Rollback info file not found: $ROLLBACK_INFO"
    exit 1
fi

source $ROLLBACK_INFO

echo "Stopping current services..."
docker-compose -f docker-compose.prod.yml down

echo "Restoring backup..."
cd /opt
tar -xzf $BACKUP_PATH

echo "Starting services..."
cd $PROJECT_DIR
docker-compose -f docker-compose.prod.yml up -d

echo "Waiting for services..."
sleep 60

echo "Performing health check..."
if curl -f http://localhost/health >/dev/null 2>&1; then
    echo "Rollback completed successfully!"
else
    echo "Warning: Health check failed after rollback"
fi
```

## Monitoring and Maintenance

### System Monitoring

#### Prometheus Configuration (`monitoring/prometheus.yml`)

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'recoater-hmi-backend'
    static_configs:
      - targets: ['backend-1:8000', 'backend-2:8000']
    scrape_interval: 5s
    metrics_path: '/metrics'

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### Alert Rules (`monitoring/alert_rules.yml`)

```yaml
groups:
  - name: recoater-hmi-alerts
    rules:
      - alert: BackendDown
        expr: up{job="recoater-hmi-backend"} == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "Backend service is down"
          description: "Backend service {{ $labels.instance }} has been down for more than 30 seconds"

      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      - alert: OPCUAConnectionLost
        expr: opcua_connection_status == 0
        for: 10s
        labels:
          severity: critical
        annotations:
          summary: "OPC UA connection lost"
          description: "Connection to PLC has been lost"

      - alert: MultiLayerJobFailed
        expr: multilayer_job_failures_total > 0
        for: 0s
        labels:
          severity: critical
        annotations:
          summary: "Multi-layer job failure"
          description: "A multi-layer print job has failed"
```

### Log Management

#### Centralized Logging (`monitoring/fluentd.conf`)

```conf
<source>
  @type tail
  path /fluentd/log/backend/*.log
  pos_file /fluentd/log/backend.log.pos
  tag backend.*
  format json
</source>

<source>
  @type tail
  path /fluentd/log/nginx/access.log
  pos_file /fluentd/log/nginx.access.log.pos
  tag nginx.access
  format nginx
</source>

<filter backend.**>
  @type record_transformer
  <record>
    hostname "#{Socket.gethostname}"
    service backend
  </record>
</filter>

<match backend.**>
  @type elasticsearch
  host elasticsearch
  port 9200
  index_name recoater-hmi-backend
  type_name _doc
</match>

<match nginx.**>
  @type elasticsearch
  host elasticsearch
  port 9200
  index_name recoater-hmi-nginx
  type_name _doc
</match>
```

### Health Checks

#### Application Health Check (`backend/app/health.py`)

```python
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import asyncio
import time
from app.services.opcua_coordinator import opcua_coordinator
from app.dependencies import get_recoater_client

router = APIRouter()

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """Comprehensive health check endpoint"""
    start_time = time.time()
    
    health_status = {
        "status": "healthy",
        "timestamp": start_time,
        "checks": {}
    }
    
    # Check OPC UA connection
    try:
        opcua_status = opcua_coordinator.is_connected()
        if opcua_status:
            # Test read operation
            test_value = await opcua_coordinator.read_variable("job_active")
            health_status["checks"]["opcua"] = {
                "status": "healthy",
                "connected": True,
                "test_read": test_value is not None
            }
        else:
            health_status["checks"]["opcua"] = {
                "status": "unhealthy",
                "connected": False,
                "error": "Not connected"
            }
            health_status["status"] = "degraded"
    except Exception as e:
        health_status["checks"]["opcua"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "degraded"
    
    # Check Aerosint connection
    try:
        recoater_client = get_recoater_client()
        aerosint_status = recoater_client.get_print_job_status()
        health_status["checks"]["aerosint"] = {
            "status": "healthy",
            "recoater_state": aerosint_status.get("state", "unknown")
        }
    except Exception as e:
        health_status["checks"]["aerosint"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "degraded"
    
    # Check system resources
    import psutil
    health_status["checks"]["system"] = {
        "status": "healthy",
        "cpu_percent": psutil.cpu_percent(),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_percent": psutil.disk_usage('/').percent
    }
    
    # Overall response time
    health_status["response_time_ms"] = (time.time() - start_time) * 1000
    
    # Return appropriate HTTP status code
    if health_status["status"] == "unhealthy":
        raise HTTPException(status_code=503, detail=health_status)
    elif health_status["status"] == "degraded":
        raise HTTPException(status_code=200, detail=health_status)
    
    return health_status

@router.get("/ready")
async def readiness_check() -> Dict[str, Any]:
    """Kubernetes readiness probe endpoint"""
    # Check if service is ready to accept traffic
    checks = []
    
    # Check critical dependencies
    if opcua_coordinator.is_connected():
        checks.append("opcua")
    
    try:
        recoater_client = get_recoater_client()
        recoater_client.get_print_job_status()
        checks.append("aerosint")
    except:
        pass
    
    if len(checks) >= 1:  # At least one critical service available
        return {"status": "ready", "services": checks}
    else:
        raise HTTPException(status_code=503, detail={"status": "not ready"})

@router.get("/live")
async def liveness_check() -> Dict[str, Any]:
    """Kubernetes liveness probe endpoint"""
    # Simple check that the application is alive
    return {"status": "alive", "timestamp": time.time()}
```

### Backup and Recovery

#### Automated Backup Script (`scripts/backup.sh`)

```bash
#!/bin/bash

set -e

# Configuration
BACKUP_DIR="/opt/backups/recoater-hmi"
RETENTION_DAYS=30
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "Starting automated backup: $TIMESTAMP"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup application data
tar -czf $BACKUP_DIR/app_backup_$TIMESTAMP.tar.gz \
    --exclude='logs/*' \
    --exclude='node_modules' \
    --exclude='venv' \
    --exclude='*.pyc' \
    /opt/recoater-hmi

# Backup Docker volumes
docker run --rm -v recoater-hmi_redis_data:/data -v $BACKUP_DIR:/backup \
    alpine tar czf /backup/redis_backup_$TIMESTAMP.tar.gz -C /data .

# Backup configuration
cp /opt/recoater-hmi/.env.production $BACKUP_DIR/env_backup_$TIMESTAMP

# Cleanup old backups
find $BACKUP_DIR -name "*backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "Backup completed: $TIMESTAMP"
```

## Security Considerations

### SSL/TLS Configuration

```bash
# Generate SSL certificate (for staging/development)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout staging.key \
    -out staging.crt \
    -subj "/CN=staging.recoater-hmi.local"

# For production, use Let's Encrypt or commercial certificates
certbot certonly --standalone -d production.recoater-hmi.com
```

### Environment Security

1. **Secrets Management**: Use Docker secrets or external secret management
2. **Network Isolation**: Separate networks for different service tiers
3. **Access Control**: Implement proper user authentication and authorization
4. **Audit Logging**: Log all administrative actions
5. **Regular Updates**: Keep all components updated with security patches

### Security Checklist

- [ ] All default passwords changed
- [ ] SSL/TLS certificates properly configured
- [ ] Firewall rules implemented
- [ ] Regular security updates applied
- [ ] Backup encryption enabled
- [ ] Access logs monitored
- [ ] Intrusion detection configured
- [ ] Security headers implemented

This deployment guide provides a comprehensive approach to deploying the multi-layer print job system across different environments while maintaining security, reliability, and maintainability.
