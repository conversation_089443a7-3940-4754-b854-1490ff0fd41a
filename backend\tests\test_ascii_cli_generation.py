"""
Test suite for ASCII CLI generation functionality.

This module tests the new ASCII CLI generation methods that create
hardware-compatible CLI files instead of binary format.
"""

import pytest
from services.cli_parser import CliParserService, <PERSON>li<PERSON>ayer, <PERSON><PERSON><PERSON>, <PERSON>, Point


class TestAsciiCliGeneration:
    """Test ASCII CLI generation methods."""

    def setup_method(self):
        """Set up test fixtures."""
        self.parser = CliParserService()
        
        # Create test layer with sample data
        self.test_layer = CliLayer(
            z_height=16.0,
            polylines=[
                Polyline(
                    part_id=1,
                    direction=1,
                    points=[
                        Point(x=100.0, y=200.0),
                        Point(x=150.0, y=250.0),
                        Point(x=200.0, y=200.0)
                    ]
                )
            ],
            hatches=[
                Hatch(
                    group_id=1,
                    lines=[
                        (Point(x=50.0, y=50.0), Point(x=100.0, y=100.0)),
                        (Point(x=60.0, y=60.0), Point(x=110.0, y=110.0))
                    ]
                )
            ]
        )

    def test_generate_single_layer_ascii_cli_basic(self):
        """Test basic ASCII CLI generation for a single layer."""
        cli_data = self.parser.generate_single_layer_ascii_cli(self.test_layer)
        
        # Decode to string for easier testing
        cli_content = cli_data.decode('ascii')
        
        # Check that it contains expected header elements
        assert "$$HEADERSTART" in cli_content
        assert "$$ASCII" in cli_content
        assert "$$HEADEREND" in cli_content
        assert "$$GEOMETRYSTART" in cli_content

        # Check layer command
        assert "$$LAYER/16.0" in cli_content

        # Check proper termination
        assert "$$GEOMETRYEND" in cli_content
        
        # Check polyline command
        assert "$$POLYLINE/1,1,3" in cli_content
        assert "100.00000,200.00000,150.00000,250.00000,200.00000,200.00000" in cli_content
        
        # Check hatch command
        assert "$$HATCHES/1,2" in cli_content
        assert "50.00000,50.00000,100.00000,100.00000,60.00000,60.00000,110.00000,110.00000" in cli_content

    def test_generate_single_layer_ascii_cli_custom_header(self):
        """Test ASCII CLI generation with custom header."""
        custom_header = [
            "$$HEADERSTART",
            "$$ASCII",
            "$$CUSTOM/test",
            "$$HEADEREND"
        ]
        
        cli_data = self.parser.generate_single_layer_ascii_cli(self.test_layer, custom_header)
        cli_content = cli_data.decode('ascii')
        
        # Check custom header is used
        assert "$$CUSTOM/test" in cli_content
        assert "$$LAYER/16.0" in cli_content

    def test_generate_ascii_cli_from_layer_range(self):
        """Test ASCII CLI generation for multiple layers."""
        # Create second test layer
        layer2 = CliLayer(
            z_height=32.0,
            polylines=[
                Polyline(
                    part_id=2,
                    direction=0,
                    points=[
                        Point(x=300.0, y=400.0),
                        Point(x=350.0, y=450.0)
                    ]
                )
            ],
            hatches=[]
        )
        
        layers = [self.test_layer, layer2]
        cli_data = self.parser.generate_ascii_cli_from_layer_range(layers)
        cli_content = cli_data.decode('ascii')
        
        # Check header
        assert "$$HEADERSTART" in cli_content
        assert "$$LAYERS/000002" in cli_content  # Should show 2 layers
        
        # Check both layers are present
        assert "$$LAYER/16.0" in cli_content
        assert "$$LAYER/32.0" in cli_content
        
        # Check both polylines are present
        assert "$$POLYLINE/1,1,3" in cli_content
        assert "$$POLYLINE/2,0,2" in cli_content

    def test_generate_ascii_cli_empty_layer(self):
        """Test ASCII CLI generation with empty layer."""
        empty_layer = CliLayer(z_height=0.0, polylines=[], hatches=[])
        
        cli_data = self.parser.generate_single_layer_ascii_cli(empty_layer)
        cli_content = cli_data.decode('ascii')
        
        # Should still have header and layer command
        assert "$$HEADERSTART" in cli_content
        assert "$$LAYER/0.0" in cli_content
        
        # Should not have polyline or hatch commands
        assert "$$POLYLINE" not in cli_content
        assert "$$HATCHES" not in cli_content

    def test_generate_ascii_cli_empty_layer_range_error(self):
        """Test that empty layer range raises error."""
        with pytest.raises(Exception) as exc_info:
            self.parser.generate_ascii_cli_from_layer_range([])
        
        assert "empty layer range" in str(exc_info.value).lower()

    def test_ascii_cli_format_matches_example(self):
        """Test that generated ASCII CLI format matches the example structure."""
        cli_data = self.parser.generate_single_layer_ascii_cli(self.test_layer)
        cli_content = cli_data.decode('ascii')
        
        lines = cli_content.split('\n')
        
        # Check structure matches example file format
        header_end_found = False
        geometry_start_found = False
        layer_found = False
        
        for line in lines:
            if line == "$$HEADEREND":
                header_end_found = True
            elif line == "$$GEOMETRYSTART":
                geometry_start_found = True
            elif line.startswith("$$LAYER/"):
                layer_found = True
                
        assert header_end_found, "$$HEADEREND not found"
        assert geometry_start_found, "$$GEOMETRYSTART not found"
        assert layer_found, "$$LAYER command not found"

    def test_ascii_cli_coordinate_precision(self):
        """Test that coordinates are formatted with proper precision."""
        # Create layer with precise coordinates
        precise_layer = CliLayer(
            z_height=1.23456,
            polylines=[
                Polyline(
                    part_id=1,
                    direction=1,
                    points=[Point(x=123.456789, y=987.654321)]
                )
            ],
            hatches=[]
        )
        
        cli_data = self.parser.generate_single_layer_ascii_cli(precise_layer)
        cli_content = cli_data.decode('ascii')
        
        # Check that coordinates are formatted to 5 decimal places
        assert "123.45679" in cli_content
        assert "987.65432" in cli_content
        assert "$$LAYER/1.23456" in cli_content
