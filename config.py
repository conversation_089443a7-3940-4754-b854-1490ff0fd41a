#!/usr/bin/env python3
"""
Configuration for Simple Connection Test
========================================

This file contains the basic configuration settings for the simple connection test.
It defines the API endpoint and timeout settings for connecting to the recoater hardware.
"""

# Recoater API Configuration
# These settings should match your recoater hardware setup
API_BASE_URL = "http://*************:8080"
API_TIMEOUT = 10.0  # seconds

# For development/testing, you can change these values:
# API_BASE_URL = "http://localhost:8000"  # For testing with mock backend
# API_TIMEOUT = 5.0
